Metadata-Version: 2.4
Name: langvm
Version: 0.1.0
Summary: Universal programming language version manager
Home-page: https://github.com/yourusername/langvm
Author: LangVM Team
Author-email: <EMAIL>
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Tools
Classifier: Topic :: System :: Installation/Setup
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: click>=8.0.0
Requires-Dist: requests>=2.28.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: packaging>=21.0
Requires-Dist: rich>=12.0.0
Requires-Dist: platformdirs>=3.0.0
Requires-Dist: pytest>=7.0.0
Requires-Dist: pytest-cov>=4.0.0
Requires-Dist: black>=22.0.0
Requires-Dist: flake8>=5.0.0
Requires-Dist: mypy>=1.0.0
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license-file
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# LangVM - Universal Language Version Manager

LangVM is a universal programming language version manager that allows you to easily install, manage, and switch between different versions of popular programming languages like Java, Python, Go, Node.js, and more.

## Features

- 🚀 **Multi-language support**: Manage Java, Python, Go, Node.js, Rust, and more
- 📦 **Easy installation**: Simple commands to install any version
- 🔄 **Quick switching**: Switch between versions instantly
- 🌍 **Cross-platform**: Works on Windows, macOS, and Linux
- ⚙️ **Configurable**: Customize settings and add new language adapters
- 🔧 **Environment management**: Automatic PATH and environment variable management

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/langvm.git
cd langvm

# Install dependencies
pip install -r requirements.txt

# Install LangVM
python setup.py install
```

## Quick Start

```bash
# List available languages
langvm list-languages

# List available versions for a language
langvm list java

# Install a specific version
langvm install java 17.0.2

# Switch to a version
langvm use java 17.0.2

# Show current active versions
langvm current

# Uninstall a version
langvm uninstall java 17.0.2
```

## Supported Languages

- ☕ **Java** (OpenJDK, Oracle JDK)
- 🐍 **Python** (CPython, PyPy)
- 🐹 **Go** (Official releases)
- 📦 **Node.js** (Official releases, LTS)
- 🦀 **Rust** (Stable, Beta, Nightly)
- 💎 **Ruby** (Official releases)

## Architecture

LangVM uses a plugin-based architecture where each language has its own adapter that handles:
- Version discovery and listing
- Download and installation
- Environment setup
- Version switching

## Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests.

## License

MIT License - see LICENSE file for details.
