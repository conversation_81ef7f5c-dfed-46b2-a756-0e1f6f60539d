#!/usr/bin/env python3
"""
LangVM 中国用户安装脚本
针对中国网络环境优化的安装程序
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_info(msg):
    print(f"[信息] {msg}")

def print_success(msg):
    print(f"[成功] {msg}")

def print_warning(msg):
    print(f"[警告] {msg}")

def print_error(msg):
    print(f"[错误] {msg}")

def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 8):
        print_error("需要 Python 3.8 或更高版本")
        print_info(f"当前版本: {sys.version}")
        return False
    
    print_success(f"Python 版本检查通过: {sys.version}")
    return True

def check_pip():
    """检查 pip 是否可用"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print_success("pip 可用")
        return True
    except subprocess.CalledProcessError:
        print_error("pip 不可用，请先安装 pip")
        return False

def configure_pip_mirror():
    """配置 pip 使用清华镜像"""
    try:
        pip_dir = Path.home() / ".pip"
        pip_dir.mkdir(exist_ok=True)
        
        pip_conf = pip_dir / "pip.conf"
        if platform.system() == "Windows":
            pip_conf = pip_dir / "pip.ini"
        
        config_content = """[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
"""
        
        with open(pip_conf, "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print_success("已配置 pip 使用清华大学镜像")
        return True
        
    except Exception as e:
        print_warning(f"配置 pip 镜像失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print_info("安装依赖包...")
    
    try:
        # 升级 pip
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True)
        
        # 安装依赖
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        
        print_success("依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print_error(f"依赖包安装失败: {e}")
        return False

def install_langvm():
    """安装 LangVM"""
    print_info("安装 LangVM...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-e", "."
        ], check=True)
        
        print_success("LangVM 安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print_error(f"LangVM 安装失败: {e}")
        return False

def test_installation():
    """测试安装"""
    print_info("测试安装...")
    
    try:
        result = subprocess.run([
            "langvm", "--version"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print_success("LangVM 安装测试通过")
            return True
        else:
            print_error("LangVM 安装测试失败")
            return False
            
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
        print_warning("无法直接运行 langvm 命令，可能需要重启终端")
        return True

def setup_shell_integration():
    """设置 shell 集成"""
    print_info("设置 shell 集成...")
    
    shell_script = """
# LangVM 配置
export PATH="$HOME/.local/bin:$PATH"

# LangVM 环境函数
langvm_env() {
    if command -v langvm >/dev/null 2>&1; then
        eval "$(langvm env)"
    fi
}

# 自动加载 LangVM 环境
langvm_env
"""
    
    try:
        if platform.system() == "Windows":
            print_info("Windows 用户请手动将以下内容添加到 PowerShell 配置文件:")
            print("Invoke-Expression (langvm env --shell powershell)")
        else:
            # Linux/macOS
            bashrc = Path.home() / ".bashrc"
            if bashrc.exists():
                with open(bashrc, "a", encoding="utf-8") as f:
                    f.write(shell_script)
                print_success("已添加到 ~/.bashrc")
            
            zshrc = Path.home() / ".zshrc"
            if zshrc.exists():
                with open(zshrc, "a", encoding="utf-8") as f:
                    f.write(shell_script)
                print_success("已添加到 ~/.zshrc")
        
        return True
        
    except Exception as e:
        print_warning(f"Shell 集成设置失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*60)
    print_success("LangVM 安装完成！")
    print("\n接下来的步骤:")
    print("1. 重启终端或运行: source ~/.bashrc")
    print("2. 运行演示: python demo.py")
    print("3. 查看帮助: langvm --help")
    print("4. 查看支持的语言: langvm languages")
    print("5. 安装 Java: langvm install java 17")
    print("\n更多信息请查看 README_CN.md")

def main():
    """主安装函数"""
    print("="*60)
    print("LangVM 中国用户安装程序")
    print("Universal Language Version Manager - China Edition")
    print("="*60)
    
    # 检查当前目录
    if not Path("setup.py").exists():
        print_error("请在 LangVM 项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查 pip
    if not check_pip():
        sys.exit(1)
    
    # 配置 pip 镜像
    configure_pip_mirror()
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 安装 LangVM
    if not install_langvm():
        sys.exit(1)
    
    # 测试安装
    test_installation()
    
    # 设置 shell 集成
    setup_shell_integration()
    
    # 显示后续步骤
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_error("\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print_error(f"安装过程中出现错误: {e}")
        sys.exit(1)
