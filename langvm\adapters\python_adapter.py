"""
Python language adapter for LangVM
"""

import json
import platform
import tarfile
import zipfile
import subprocess
from pathlib import Path
from typing import List, Optional, Dict, Any
import re

import requests

from ..core.base import LanguageAdapter, LanguageVersion, InstallationInfo
from ..utils.logger import get_logger


class PythonAdapter(LanguageAdapter):
    """Adapter for managing Python versions"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = get_logger(__name__)
        self.base_url = "https://www.python.org/ftp/python/"

    def get_language_name(self) -> str:
        return "python"

    def list_available_versions(self) -> List[LanguageVersion]:
        """List available Python versions"""
        try:
            # Get list of versions from python.org FTP
            response = requests.get(self.base_url, timeout=30)
            response.raise_for_status()
            
            # Parse HTML to find version directories
            version_pattern = r'href="(\d+\.\d+\.\d+)/"'
            matches = re.findall(version_pattern, response.text)
            
            versions = []
            for version in matches:
                # Skip pre-release versions (alpha, beta, rc)
                if any(x in version.lower() for x in ['a', 'b', 'rc']):
                    continue
                
                download_url = self._get_download_url(version)
                if download_url:
                    version_obj = LanguageVersion(
                        language="python",
                        version=version,
                        url=download_url,
                        metadata={
                            "type": "official",
                            "os": self._get_os_name(),
                            "arch": self._get_architecture()
                        }
                    )
                    
                    # Mark LTS versions (3.9, 3.10, 3.11, etc.)
                    major, minor, _ = version.split('.')
                    if int(major) == 3 and int(minor) >= 9:
                        version_obj.is_lts = True
                    
                    versions.append(version_obj)
            
            # Sort by version number (newest first)
            versions.sort(key=lambda v: tuple(map(int, v.version.split('.'))), reverse=True)
            
            # Mark the latest version
            if versions:
                versions[0].is_latest = True
            
            return versions
            
        except Exception as e:
            self.logger.error(f"Failed to list Python versions: {e}")
            return []

    def _get_download_url(self, version: str) -> Optional[str]:
        """Get download URL for a specific Python version"""
        os_name = self._get_os_name()
        arch = self._get_architecture()
        
        if os_name == "windows":
            if arch == "x64":
                filename = f"python-{version}-amd64.exe"
            else:
                filename = f"python-{version}.exe"
        elif os_name == "macos":
            filename = f"python-{version}-macos11.pkg"
        else:  # Linux
            filename = f"Python-{version}.tgz"
        
        return f"{self.base_url}{version}/{filename}"

    def _get_os_name(self) -> str:
        """Get OS name"""
        system = platform.system().lower()
        if system == "windows":
            return "windows"
        elif system == "darwin":
            return "macos"
        else:
            return "linux"

    def _get_architecture(self) -> str:
        """Get architecture"""
        machine = platform.machine().lower()
        if machine in ["x86_64", "amd64"]:
            return "x64"
        elif machine in ["aarch64", "arm64"]:
            return "arm64"
        else:
            return "x86"

    def download_version(self, version: str, install_path: Path) -> bool:
        """Download and install Python version"""
        try:
            version_info = self.get_version_info(version)
            if not version_info:
                self.logger.error(f"Version {version} not found")
                return False

            # Create a downloader instance
            from ..utils.downloader import Downloader
            from ..core.config import Config
            downloader = Downloader(Config())

            # Download the installer/archive
            installer_name = downloader.get_filename_from_url(version_info.url)
            installer_path = install_path.parent / f"python-{version}-{installer_name}"
            
            if not downloader.download_file(version_info.url, installer_path):
                return False

            # Install based on OS
            os_name = self._get_os_name()
            if os_name == "windows":
                success = self._install_windows(installer_path, install_path)
            elif os_name == "macos":
                success = self._install_macos(installer_path, install_path)
            else:
                success = self._install_linux(installer_path, install_path)

            # Clean up installer
            installer_path.unlink(missing_ok=True)
            
            return success

        except Exception as e:
            self.logger.error(f"Failed to download Python {version}: {e}")
            return False

    def _install_windows(self, installer_path: Path, install_path: Path) -> bool:
        """Install Python on Windows"""
        try:
            # Run the installer silently
            cmd = [
                str(installer_path),
                "/quiet",
                f"TargetDir={install_path}",
                "InstallAllUsers=0",
                "PrependPath=0",
                "Include_test=0"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Failed to install Python on Windows: {e}")
            return False

    def _install_macos(self, installer_path: Path, install_path: Path) -> bool:
        """Install Python on macOS"""
        try:
            # Extract and install the package
            # This is a simplified implementation
            # In practice, you might need to handle .pkg files differently
            self.logger.warning("macOS installation not fully implemented")
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to install Python on macOS: {e}")
            return False

    def _install_linux(self, archive_path: Path, install_path: Path) -> bool:
        """Install Python on Linux by compiling from source"""
        try:
            # Extract source code
            with tarfile.open(archive_path, 'r:gz') as tar:
                tar.extractall(install_path.parent)
            
            # Find extracted directory
            source_dir = None
            for item in install_path.parent.iterdir():
                if item.is_dir() and item.name.startswith('Python-'):
                    source_dir = item
                    break
            
            if not source_dir:
                self.logger.error("Could not find extracted Python source")
                return False

            # Configure, compile and install
            commands = [
                ["./configure", f"--prefix={install_path}", "--enable-optimizations"],
                ["make", "-j4"],
                ["make", "install"]
            ]
            
            for cmd in commands:
                result = subprocess.run(
                    cmd, 
                    cwd=source_dir, 
                    capture_output=True, 
                    text=True
                )
                if result.returncode != 0:
                    self.logger.error(f"Command failed: {' '.join(cmd)}")
                    self.logger.error(f"Error: {result.stderr}")
                    return False
            
            # Clean up source directory
            import shutil
            shutil.rmtree(source_dir)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to install Python on Linux: {e}")
            return False

    def activate_version(self, version: str, install_path: Path) -> bool:
        """Activate Python version"""
        return self.verify_installation(version, install_path)

    def deactivate_version(self, version: str, install_path: Path) -> bool:
        """Deactivate Python version"""
        return True

    def uninstall_version(self, version: str, install_path: Path) -> bool:
        """Uninstall Python version"""
        return True

    def get_installed_versions(self, base_path: Path) -> List[InstallationInfo]:
        """Get installed Python versions"""
        installed = []
        
        if not base_path.exists():
            return installed

        for version_dir in base_path.iterdir():
            if version_dir.is_dir():
                if self.verify_installation(version_dir.name, version_dir):
                    installed.append(InstallationInfo(
                        language="python",
                        version=version_dir.name,
                        install_path=version_dir
                    ))
        
        return installed

    def verify_installation(self, version: str, install_path: Path) -> bool:
        """Verify Python installation"""
        # Check for python executable
        if platform.system() == "Windows":
            python_exe = install_path / "python.exe"
        else:
            python_exe = install_path / "bin" / "python3"
            if not python_exe.exists():
                python_exe = install_path / "bin" / "python"
        
        return python_exe.exists() and python_exe.is_file()

    def get_version_info(self, version: str) -> Optional[LanguageVersion]:
        """Get information about a specific Python version"""
        download_url = self._get_download_url(version)
        if download_url:
            return LanguageVersion(
                language="python",
                version=version,
                url=download_url,
                metadata={
                    "type": "official",
                    "os": self._get_os_name(),
                    "arch": self._get_architecture()
                }
            )
        return None

    def get_executable_path(self, version: str, install_path: Path) -> Optional[Path]:
        """Get path to Python executable"""
        if platform.system() == "Windows":
            python_exe = install_path / "python.exe"
        else:
            python_exe = install_path / "bin" / "python3"
            if not python_exe.exists():
                python_exe = install_path / "bin" / "python"
        
        if python_exe.exists():
            return python_exe
        return None
