"""
Python language adapter for LangVM
"""

import json
import platform
import tarfile
import zipfile
import subprocess
from pathlib import Path
from typing import List, Optional, Dict, Any
import re

import requests

from ..core.base import LanguageAdapter, LanguageVersion, InstallationInfo
from ..utils.logger import get_logger


class PythonAdapter(LanguageAdapter):
    """Adapter for managing Python versions"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = get_logger(__name__)
        self.base_url = "https://www.python.org/ftp/python/"

    def get_language_name(self) -> str:
        return "python"

    def list_available_versions(self) -> List[LanguageVersion]:
        """List available Python versions"""
        try:
            # Get list of versions from python.org FTP
            response = requests.get(self.base_url, timeout=30)
            response.raise_for_status()
            
            # Parse HTML to find version directories
            version_pattern = r'href="(\d+\.\d+\.\d+)/"'
            matches = re.findall(version_pattern, response.text)
            
            versions = []
            for version in matches:
                # Skip pre-release versions (alpha, beta, rc)
                if any(x in version.lower() for x in ['a', 'b', 'rc']):
                    continue
                
                download_url = self._get_download_url(version)
                if download_url:
                    version_obj = LanguageVersion(
                        language="python",
                        version=version,
                        url=download_url,
                        metadata={
                            "type": "official",
                            "os": self._get_os_name(),
                            "arch": self._get_architecture()
                        }
                    )
                    
                    # Mark LTS versions (3.9, 3.10, 3.11, etc.)
                    major, minor, _ = version.split('.')
                    if int(major) == 3 and int(minor) >= 9:
                        version_obj.is_lts = True
                    
                    versions.append(version_obj)
            
            # Sort by version number (newest first)
            versions.sort(key=lambda v: tuple(map(int, v.version.split('.'))), reverse=True)
            
            # Mark the latest version
            if versions:
                versions[0].is_latest = True
            
            return versions
            
        except Exception as e:
            self.logger.error(f"Failed to list Python versions: {e}")
            return []

    def _get_download_url(self, version: str) -> Optional[str]:
        """Get download URL for a specific Python version"""
        os_name = self._get_os_name()
        arch = self._get_architecture()
        
        if os_name == "windows":
            if arch == "x64":
                filename = f"python-{version}-amd64.exe"
            else:
                filename = f"python-{version}.exe"
        elif os_name == "macos":
            filename = f"python-{version}-macos11.pkg"
        else:  # Linux
            filename = f"Python-{version}.tgz"
        
        return f"{self.base_url}{version}/{filename}"

    def _get_os_name(self) -> str:
        """Get OS name"""
        system = platform.system().lower()
        if system == "windows":
            return "windows"
        elif system == "darwin":
            return "macos"
        else:
            return "linux"

    def _get_architecture(self) -> str:
        """Get architecture"""
        machine = platform.machine().lower()
        if machine in ["x86_64", "amd64"]:
            return "x64"
        elif machine in ["aarch64", "arm64"]:
            return "arm64"
        else:
            return "x86"

    def download_version(self, version: str, install_path: Path) -> bool:
        """Download and install Python version"""
        try:
            version_info = self.get_version_info(version)
            if not version_info:
                self.logger.error(f"Version {version} not found")
                return False

            # Create a downloader instance
            from ..utils.downloader import Downloader
            from ..core.config import Config
            downloader = Downloader(Config())

            # Download the installer/archive
            installer_name = downloader.get_filename_from_url(version_info.url)
            installer_path = install_path.parent / f"python-{version}-{installer_name}"
            
            if not downloader.download_file(version_info.url, installer_path):
                return False

            # Install based on OS
            os_name = self._get_os_name()
            if os_name == "windows":
                success = self._install_windows(installer_path, install_path)
            elif os_name == "macos":
                success = self._install_macos(installer_path, install_path)
            else:
                success = self._install_linux(installer_path, install_path)

            # Clean up installer
            installer_path.unlink(missing_ok=True)
            
            return success

        except Exception as e:
            self.logger.error(f"Failed to download Python {version}: {e}")
            return False

    def _install_windows(self, installer_path: Path, install_path: Path) -> bool:
        """Install Python on Windows"""
        try:
            # Run the installer silently
            cmd = [
                str(installer_path),
                "/quiet",
                f"TargetDir={install_path}",
                "InstallAllUsers=0",
                "PrependPath=0",
                "Include_test=0"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Failed to install Python on Windows: {e}")
            return False

    def _install_macos(self, installer_path: Path, install_path: Path) -> bool:
        """Install Python on macOS"""
        try:
            # Extract and install the package
            # This is a simplified implementation
            # In practice, you might need to handle .pkg files differently
            self.logger.warning("macOS installation not fully implemented")
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to install Python on macOS: {e}")
            return False

    def _install_linux(self, archive_path: Path, install_path: Path) -> bool:
        """Install Python on Linux by compiling from source"""
        try:
            # Extract source code
            with tarfile.open(archive_path, 'r:gz') as tar:
                tar.extractall(install_path.parent)
            
            # Find extracted directory
            source_dir = None
            for item in install_path.parent.iterdir():
                if item.is_dir() and item.name.startswith('Python-'):
                    source_dir = item
                    break
            
            if not source_dir:
                self.logger.error("Could not find extracted Python source")
                return False

            # Configure, compile and install
            commands = [
                ["./configure", f"--prefix={install_path}", "--enable-optimizations"],
                ["make", "-j4"],
                ["make", "install"]
            ]
            
            for cmd in commands:
                result = subprocess.run(
                    cmd, 
                    cwd=source_dir, 
                    capture_output=True, 
                    text=True
                )
                if result.returncode != 0:
                    self.logger.error(f"Command failed: {' '.join(cmd)}")
                    self.logger.error(f"Error: {result.stderr}")
                    return False
            
            # Clean up source directory
            import shutil
            shutil.rmtree(source_dir)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to install Python on Linux: {e}")
            return False

    def activate_version(self, version: str, install_path: Path) -> bool:
        """Activate Python version - 需要管理员权限"""
        try:
            if not self.verify_installation(version, install_path):
                self.logger.error(f"Python {version} installation verification failed")
                return False

            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to activate Python version")
                return False

            # 设置系统环境变量
            if not self._set_python_env_vars(install_path):
                self.logger.error("Failed to set Python environment variables")
                return False

            # 更新系统PATH
            python_paths = self._get_python_paths(install_path)
            for python_path in python_paths:
                if not self._add_to_system_path(str(python_path)):
                    self.logger.error(f"Failed to add {python_path} to system PATH")
                    return False

            self.logger.info(f"Successfully activated Python {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to activate Python {version}: {e}")
            return False

    def deactivate_version(self, version: str, install_path: Path) -> bool:
        """Deactivate Python version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to deactivate Python version")
                return False

            # 从系统PATH中移除Python路径
            python_paths = self._get_python_paths(install_path)
            for python_path in python_paths:
                if not self._remove_from_system_path(str(python_path)):
                    self.logger.warning(f"Failed to remove {python_path} from system PATH")

            # 清除Python环境变量
            if not self._remove_python_env_vars():
                self.logger.warning("Failed to remove Python environment variables")

            self.logger.info(f"Successfully deactivated Python {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deactivate Python {version}: {e}")
            return False

    def uninstall_version(self, version: str, install_path: Path) -> bool:
        """Uninstall Python version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to uninstall Python version")
                return False

            # 先停用版本
            self.deactivate_version(version, install_path)

            # 删除安装目录由manager处理
            self.logger.info(f"Python {version} marked for uninstallation")
            return True

        except Exception as e:
            self.logger.error(f"Failed to uninstall Python {version}: {e}")
            return False

    def get_installed_versions(self, base_path: Path) -> List[InstallationInfo]:
        """Get installed Python versions"""
        installed = []
        
        if not base_path.exists():
            return installed

        for version_dir in base_path.iterdir():
            if version_dir.is_dir():
                if self.verify_installation(version_dir.name, version_dir):
                    installed.append(InstallationInfo(
                        language="python",
                        version=version_dir.name,
                        install_path=version_dir
                    ))
        
        return installed

    def verify_installation(self, version: str, install_path: Path) -> bool:
        """Verify Python installation"""
        # Check for python executable
        if platform.system() == "Windows":
            python_exe = install_path / "python.exe"
        else:
            python_exe = install_path / "bin" / "python3"
            if not python_exe.exists():
                python_exe = install_path / "bin" / "python"
        
        return python_exe.exists() and python_exe.is_file()

    def get_version_info(self, version: str) -> Optional[LanguageVersion]:
        """Get information about a specific Python version"""
        download_url = self._get_download_url(version)
        if download_url:
            return LanguageVersion(
                language="python",
                version=version,
                url=download_url,
                metadata={
                    "type": "official",
                    "os": self._get_os_name(),
                    "arch": self._get_architecture()
                }
            )
        return None

    def get_executable_path(self, version: str, install_path: Path) -> Optional[Path]:
        """Get path to Python executable"""
        if platform.system() == "Windows":
            python_exe = install_path / "python.exe"
        else:
            python_exe = install_path / "bin" / "python3"
            if not python_exe.exists():
                python_exe = install_path / "bin" / "python"
        
        if python_exe.exists():
            return python_exe
        return None

    def _check_admin_privileges(self) -> bool:
        """检查是否有管理员权限"""
        from ..utils.admin import is_admin
        return is_admin()

    def _set_python_env_vars(self, install_path: Path) -> bool:
        """设置Python环境变量"""
        from ..utils.admin import set_system_env_var

        # 设置PYTHON_HOME
        if not set_system_env_var("PYTHON_HOME", str(install_path)):
            return False

        # 设置PYTHONPATH (可选)
        if platform.system() == "Windows":
            lib_path = install_path / "Lib"
        else:
            lib_path = install_path / "lib" / f"python{self._get_python_version_short()}"

        if lib_path.exists():
            set_system_env_var("PYTHONPATH", str(lib_path))

        return True

    def _remove_python_env_vars(self) -> bool:
        """移除Python环境变量"""
        from ..utils.admin import remove_system_env_var

        success = True
        if not remove_system_env_var("PYTHON_HOME"):
            success = False
        if not remove_system_env_var("PYTHONPATH"):
            success = False

        return success

    def _get_python_paths(self, install_path: Path) -> List[Path]:
        """获取Python需要添加到PATH的路径"""
        paths = []

        if platform.system() == "Windows":
            # Windows: Python根目录和Scripts目录
            if install_path.exists():
                paths.append(install_path)
            scripts_dir = install_path / "Scripts"
            if scripts_dir.exists():
                paths.append(scripts_dir)
        else:
            # Linux/macOS: bin目录
            bin_dir = install_path / "bin"
            if bin_dir.exists():
                paths.append(bin_dir)

        return paths

    def _get_python_version_short(self) -> str:
        """获取Python短版本号 (如 3.11)"""
        # 这里简化处理，实际应该从安装路径或版本信息获取
        return "3.11"  # 默认值

    def _add_to_system_path(self, path: str) -> bool:
        """添加路径到系统PATH"""
        from ..utils.admin import add_to_system_path
        return add_to_system_path(path)

    def _remove_from_system_path(self, path: str) -> bool:
        """从系统PATH中移除路径"""
        from ..utils.admin import remove_from_system_path
        return remove_from_system_path(path)
