# LangVM Windows Setup Script

param(
    [switch]$Force = $false
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-LangVM {
    Write-Info "Installing LangVM..."
    
    # Check if Python is available
    try {
        $pythonVersion = python --version 2>&1
        Write-Info "Found Python: $pythonVersion"
    }
    catch {
        Write-Error "Python is required but not found in PATH"
        Write-Info "Please install Python from https://python.org"
        exit 1
    }
    
    # Check if pip is available
    try {
        $pipVersion = pip --version 2>&1
        Write-Info "Found pip: $pipVersion"
    }
    catch {
        Write-Error "pip is required but not found"
        exit 1
    }
    
    # Install LangVM
    try {
        if ($Force) {
            pip install --user --force-reinstall .
        } else {
            pip install --user .
        }
        Write-Success "LangVM installed successfully"
    }
    catch {
        Write-Error "Failed to install LangVM: $_"
        exit 1
    }
}

function Setup-PowerShellProfile {
    Write-Info "Setting up PowerShell profile integration..."
    
    # Get PowerShell profile path
    $profilePath = $PROFILE.CurrentUserAllHosts
    $profileDir = Split-Path $profilePath -Parent
    
    # Create profile directory if it doesn't exist
    if (!(Test-Path $profileDir)) {
        New-Item -ItemType Directory -Path $profileDir -Force | Out-Null
    }
    
    # Check if already configured
    if ((Test-Path $profilePath) -and (Get-Content $profilePath | Select-String "langvm")) {
        Write-Warning "LangVM already configured in PowerShell profile"
        return
    }
    
    # Add LangVM configuration to profile
    $langvmConfig = @"

# LangVM Configuration
# Add user scripts directory to PATH
`$userScriptsPath = [System.IO.Path]::Combine([System.Environment]::GetFolderPath("UserProfile"), "AppData", "Roaming", "Python", "Python*", "Scripts")
if (Test-Path `$userScriptsPath) {
    `$env:PATH = "`$userScriptsPath;`$env:PATH"
}

# LangVM environment function
function Invoke-LangVMEnv {
    if (Get-Command langvm -ErrorAction SilentlyContinue) {
        Invoke-Expression (langvm env --shell powershell)
    }
}

# Auto-load LangVM environment
Invoke-LangVMEnv

"@
    
    Add-Content -Path $profilePath -Value $langvmConfig
    Write-Success "Added LangVM configuration to PowerShell profile: $profilePath"
    Write-Info "Please restart PowerShell or run: . `$PROFILE"
}

function Setup-EnvironmentVariables {
    Write-Info "Setting up environment variables..."
    
    # Get user Python Scripts directory
    $pythonScriptsPath = python -c "import site; print(site.getusersitepackages().replace('site-packages', 'Scripts'))" 2>$null
    
    if ($pythonScriptsPath -and (Test-Path $pythonScriptsPath)) {
        # Get current user PATH
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        
        # Check if already in PATH
        if ($currentPath -notlike "*$pythonScriptsPath*") {
            # Add to user PATH
            $newPath = "$pythonScriptsPath;$currentPath"
            [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
            Write-Success "Added Python Scripts directory to user PATH"
        } else {
            Write-Info "Python Scripts directory already in PATH"
        }
    }
}

function Main {
    Write-Info "Starting LangVM Windows setup..."
    
    # Check if we're in the right directory
    if (!(Test-Path "setup.py")) {
        Write-Error "Please run this script from the LangVM root directory"
        exit 1
    }
    
    # Install LangVM
    Install-LangVM
    
    # Setup environment variables
    Setup-EnvironmentVariables
    
    # Setup PowerShell profile
    Setup-PowerShellProfile
    
    Write-Success "LangVM setup completed!"
    Write-Info "You can now use 'langvm' command to manage language versions"
    Write-Info "Try: langvm languages"
    Write-Warning "You may need to restart your terminal for PATH changes to take effect"
}

# Run main function
Main
