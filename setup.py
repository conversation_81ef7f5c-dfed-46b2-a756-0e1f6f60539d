#!/usr/bin/env python3
"""
Setup script for LangVM - Universal Language Version Manager
"""

from setuptools import setup, find_packages
import os

# Read the README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="langvm",
    version="0.1.0",
    author="LangVM Team",
    author_email="<EMAIL>",
    description="Universal programming language version manager",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/langvm",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Tools",
        "Topic :: System :: Installation/Setup",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "langvm=langvm.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "langvm": ["adapters/*.py", "config/*.yaml"],
    },
)
