#!/usr/bin/env python3

from langvm.adapters.java_adapter import JavaAdapter
import requests

def test_java_adapter():
    adapter = JavaAdapter({})
    
    # Test API call
    print("Testing Adoptium API...")
    url = f'{adapter.api_base}/info/available_releases'
    response = requests.get(url, timeout=30)
    data = response.json()
    print('API Response:', data)
    
    # Test version details
    print("\nTesting version details...")
    for version in [17, 21]:
        print(f'Testing version {version}')
        try:
            # Test the URL directly
            os_name = adapter._get_os_name()
            arch = adapter._get_architecture()
            url = f"{adapter.api_base}/binary/latest/{version}/ga/{os_name}/{arch}/jdk/hotspot/normal/eclipse"
            print(f'  Testing URL: {url}')

            response = requests.head(url, timeout=30)
            print(f'  Status code: {response.status_code}')
            if response.status_code == 200:
                print(f'  Redirect URL: {response.url}')

            version_info = adapter._get_version_details(version)
            print(f'  Result: {version_info}')
        except Exception as e:
            print(f'  Error: {e}')
            import traceback
            traceback.print_exc()
    
    # Test list_available_versions
    print("\nTesting list_available_versions...")
    try:
        versions = adapter.list_available_versions()
        print(f'Found {len(versions)} versions')
        for v in versions[:3]:
            print(f'  {v.version}: {v.url}')
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_java_adapter()
