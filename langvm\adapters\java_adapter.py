"""
Java language adapter for LangVM
"""

import json
import platform
import tarfile
import zipfile
from pathlib import Path
from typing import List, Optional, Dict, Any

import requests

from ..core.base import LanguageAdapter, LanguageVersion, InstallationInfo
from ..utils.logger import get_logger
from ..utils.downloader import Downloader


class JavaAdapter(LanguageAdapter):
    """Adapter for managing Java versions"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = get_logger(__name__)
        self.api_base = "https://api.adoptium.net/v3"

    def get_language_name(self) -> str:
        return "java"

    def list_available_versions(self) -> List[LanguageVersion]:
        """List available Java versions from Adoptium API"""
        try:
            # Get available releases
            url = f"{self.api_base}/info/available_releases"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            versions = []
            
            # Get LTS and current versions
            lts_versions = data.get("available_lts_releases", [])
            available_versions = data.get("available_releases", [])
            
            for version in available_versions:
                # Get detailed info for this version
                version_info = self._get_version_details(version)
                if version_info:
                    version_info.is_lts = version in lts_versions
                    if version == max(available_versions):
                        version_info.is_latest = True
                    versions.append(version_info)
            
            return sorted(versions, key=lambda v: int(v.version), reverse=True)
            
        except Exception as e:
            self.logger.error(f"Failed to list Java versions: {e}")
            return []

    def _get_version_details(self, version: int) -> Optional[LanguageVersion]:
        """Get detailed information for a specific Java version"""
        try:
            # Get the latest release for this version
            os_name = self._get_os_name()
            arch = self._get_architecture()
            
            url = f"{self.api_base}/binary/latest/{version}/ga/{os_name}/{arch}/jdk/hotspot/normal/eclipse"
            response = requests.head(url, timeout=30, allow_redirects=True)

            if response.status_code == 200:
                download_url = response.url
                return LanguageVersion(
                    language="java",
                    version=str(version),
                    url=download_url,
                    metadata={
                        "distribution": "temurin",
                        "jvm": "hotspot",
                        "os": os_name,
                        "arch": arch
                    }
                )
            
        except Exception as e:
            self.logger.debug(f"Failed to get details for Java {version}: {e}")
        
        return None

    def _get_os_name(self) -> str:
        """Get OS name for Adoptium API"""
        system = platform.system().lower()
        if system == "windows":
            return "windows"
        elif system == "darwin":
            return "mac"
        elif system == "linux":
            return "linux"
        else:
            return "linux"  # Default fallback

    def _get_architecture(self) -> str:
        """Get architecture for Adoptium API"""
        machine = platform.machine().lower()
        if machine in ["x86_64", "amd64"]:
            return "x64"
        elif machine in ["aarch64", "arm64"]:
            return "aarch64"
        elif machine in ["x86", "i386", "i686"]:
            return "x32"
        else:
            return "x64"  # Default fallback

    def download_version(self, version: str, install_path: Path) -> bool:
        """Download and install Java version"""
        try:
            # Get download URL
            version_info = self.get_version_info(version)
            if not version_info:
                self.logger.error(f"Version {version} not found")
                return False

            # Create a downloader instance
            from ..utils.downloader import Downloader
            from ..core.config import Config
            downloader = Downloader(Config())

            # Download the archive
            archive_name = downloader.get_filename_from_url(version_info.url)
            archive_path = install_path.parent / f"java-{version}-{archive_name}"

            if not downloader.download_file(version_info.url, archive_path):
                return False

            # Extract the archive
            if not self._extract_archive(archive_path, install_path):
                return False

            # Clean up archive
            archive_path.unlink(missing_ok=True)
            
            return True

        except Exception as e:
            self.logger.error(f"Failed to download Java {version}: {e}")
            return False

    def _extract_archive(self, archive_path: Path, install_path: Path) -> bool:
        """Extract Java archive"""
        try:
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(install_path.parent)
            else:
                # Assume tar.gz
                with tarfile.open(archive_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(install_path.parent)

            # Find the extracted directory and move contents to install_path
            extracted_dirs = [d for d in install_path.parent.iterdir() 
                            if d.is_dir() and d.name.startswith('jdk')]
            
            if extracted_dirs:
                extracted_dir = extracted_dirs[0]
                # Move contents to install_path
                if not install_path.exists():
                    extracted_dir.rename(install_path)
                else:
                    # Move contents
                    for item in extracted_dir.iterdir():
                        item.rename(install_path / item.name)
                    extracted_dir.rmdir()
                
                return True
            else:
                self.logger.error("Could not find extracted Java directory")
                return False

        except Exception as e:
            self.logger.error(f"Failed to extract Java archive: {e}")
            return False

    def activate_version(self, version: str, install_path: Path) -> bool:
        """Activate Java version"""
        # Verification is enough for Java - environment manager handles the rest
        return self.verify_installation(version, install_path)

    def deactivate_version(self, version: str, install_path: Path) -> bool:
        """Deactivate Java version"""
        # Environment manager handles deactivation
        return True

    def uninstall_version(self, version: str, install_path: Path) -> bool:
        """Uninstall Java version"""
        # Just return True - directory removal is handled by manager
        return True

    def get_installed_versions(self, base_path: Path) -> List[InstallationInfo]:
        """Get installed Java versions"""
        installed = []
        
        if not base_path.exists():
            return installed

        for version_dir in base_path.iterdir():
            if version_dir.is_dir():
                if self.verify_installation(version_dir.name, version_dir):
                    installed.append(InstallationInfo(
                        language="java",
                        version=version_dir.name,
                        install_path=version_dir
                    ))
        
        return installed

    def verify_installation(self, version: str, install_path: Path) -> bool:
        """Verify Java installation"""
        # Check for java executable
        java_exe = "java.exe" if platform.system() == "Windows" else "java"
        java_path = install_path / "bin" / java_exe
        
        return java_path.exists() and java_path.is_file()

    def get_version_info(self, version: str) -> Optional[LanguageVersion]:
        """Get information about a specific Java version"""
        try:
            version_int = int(version)
            return self._get_version_details(version_int)
        except ValueError:
            self.logger.error(f"Invalid Java version format: {version}")
            return None

    def get_executable_path(self, version: str, install_path: Path) -> Optional[Path]:
        """Get path to Java executable"""
        java_exe = "java.exe" if platform.system() == "Windows" else "java"
        java_path = install_path / "bin" / java_exe
        
        if java_path.exists():
            return java_path
        return None

    def get_environment_variables(self, version: str, install_path: Path) -> Dict[str, str]:
        """Get environment variables for Java"""
        return {
            "JAVA_HOME": str(install_path)
        }
