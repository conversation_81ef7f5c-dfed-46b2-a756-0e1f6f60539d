"""
Java language adapter for LangVM
"""

import json
import platform
import tarfile
import zipfile
from pathlib import Path
from typing import List, Optional, Dict, Any

import requests

from ..core.base import LanguageAdapter, LanguageVersion, InstallationInfo
from ..utils.logger import get_logger
from ..utils.downloader import Downloader


class JavaAdapter(LanguageAdapter):
    """Adapter for managing Java versions"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = get_logger(__name__)
        # 优先使用国内镜像
        self.mirror_base = "https://mirrors.tuna.tsinghua.edu.cn/Adoptium"
        self.api_base = "https://api.adoptium.net/v3"  # 用于获取版本信息
        self.use_domestic_mirror = True

    def get_language_name(self) -> str:
        return "java"

    def list_available_versions(self) -> List[LanguageVersion]:
        """List available Java versions from Adoptium API"""
        try:
            # Get available releases
            url = f"{self.api_base}/info/available_releases"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            versions = []
            
            # Get LTS and current versions
            lts_versions = data.get("available_lts_releases", [])
            available_versions = data.get("available_releases", [])
            
            for version in available_versions:
                # Get detailed info for this version
                version_info = self._get_version_details(version)
                if version_info:
                    version_info.is_lts = version in lts_versions
                    if version == max(available_versions):
                        version_info.is_latest = True
                    versions.append(version_info)
            
            return sorted(versions, key=lambda v: int(v.version), reverse=True)
            
        except Exception as e:
            self.logger.error(f"Failed to list Java versions: {e}")
            return []

    def _get_version_details(self, version: int) -> Optional[LanguageVersion]:
        """Get detailed information for a specific Java version"""
        try:
            os_name = self._get_os_name()
            arch = self._get_architecture()

            # 构建国内镜像下载URL
            if self.use_domestic_mirror:
                download_url = self._get_domestic_download_url(version, os_name, arch)
                if download_url:
                    return LanguageVersion(
                        language="java",
                        version=str(version),
                        url=download_url,
                        metadata={
                            "distribution": "temurin",
                            "jvm": "hotspot",
                            "os": os_name,
                            "arch": arch,
                            "mirror": "domestic"
                        }
                    )

            # 回退到官方API
            url = f"{self.api_base}/binary/latest/{version}/ga/{os_name}/{arch}/jdk/hotspot/normal/eclipse"
            response = requests.head(url, timeout=30, allow_redirects=True)

            if response.status_code == 200:
                download_url = response.url
                return LanguageVersion(
                    language="java",
                    version=str(version),
                    url=download_url,
                    metadata={
                        "distribution": "temurin",
                        "jvm": "hotspot",
                        "os": os_name,
                        "arch": arch,
                        "mirror": "official"
                    }
                )

        except Exception as e:
            self.logger.debug(f"Failed to get details for Java {version}: {e}")

        return None

    def _get_domestic_download_url(self, version: int, os_name: str, arch: str) -> Optional[str]:
        """构建国内镜像下载URL"""
        try:
            # 根据版本和平台构建文件名
            if os_name == "windows":
                if arch == "x64":
                    filename = f"OpenJDK{version}U-jdk_x64_windows_hotspot_{version}.0.16_8.zip"
                else:
                    filename = f"OpenJDK{version}U-jdk_x86-32_windows_hotspot_{version}.0.16_8.zip"
            elif os_name == "mac":
                if arch == "x64":
                    filename = f"OpenJDK{version}U-jdk_x64_mac_hotspot_{version}.0.16_8.tar.gz"
                elif arch == "aarch64":
                    filename = f"OpenJDK{version}U-jdk_aarch64_mac_hotspot_{version}.0.16_8.tar.gz"
            else:  # linux
                if arch == "x64":
                    filename = f"OpenJDK{version}U-jdk_x64_linux_hotspot_{version}.0.16_8.tar.gz"
                elif arch == "aarch64":
                    filename = f"OpenJDK{version}U-jdk_aarch64_linux_hotspot_{version}.0.16_8.tar.gz"
                else:
                    return None

            # 构建完整的下载URL
            return f"{self.mirror_base}/{version}/{filename}"

        except Exception as e:
            self.logger.debug(f"Failed to build domestic URL for Java {version}: {e}")
            return None

    def _get_os_name(self) -> str:
        """Get OS name for Adoptium API"""
        system = platform.system().lower()
        if system == "windows":
            return "windows"
        elif system == "darwin":
            return "mac"
        elif system == "linux":
            return "linux"
        else:
            return "linux"  # Default fallback

    def _get_architecture(self) -> str:
        """Get architecture for Adoptium API"""
        machine = platform.machine().lower()
        if machine in ["x86_64", "amd64"]:
            return "x64"
        elif machine in ["aarch64", "arm64"]:
            return "aarch64"
        elif machine in ["x86", "i386", "i686"]:
            return "x32"
        else:
            return "x64"  # Default fallback

    def download_version(self, version: str, install_path: Path) -> bool:
        """Download and install Java version"""
        try:
            # Get download URL
            version_info = self.get_version_info(version)
            if not version_info:
                self.logger.error(f"Version {version} not found")
                return False

            # Create a downloader instance
            from ..utils.downloader import Downloader
            from ..core.config import Config
            downloader = Downloader(Config())

            # Download the archive
            archive_name = downloader.get_filename_from_url(version_info.url)
            archive_path = install_path.parent / f"java-{version}-{archive_name}"

            if not downloader.download_file(version_info.url, archive_path):
                return False

            # Extract the archive
            if not self._extract_archive(archive_path, install_path):
                return False

            # Clean up archive
            archive_path.unlink(missing_ok=True)
            
            return True

        except Exception as e:
            self.logger.error(f"Failed to download Java {version}: {e}")
            return False

    def _extract_archive(self, archive_path: Path, install_path: Path) -> bool:
        """Extract Java archive"""
        try:
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(install_path.parent)
            else:
                # Assume tar.gz
                with tarfile.open(archive_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(install_path.parent)

            # Find the extracted directory and move contents to install_path
            extracted_dirs = [d for d in install_path.parent.iterdir() 
                            if d.is_dir() and d.name.startswith('jdk')]
            
            if extracted_dirs:
                extracted_dir = extracted_dirs[0]
                # Move contents to install_path
                if not install_path.exists():
                    extracted_dir.rename(install_path)
                else:
                    # Move contents
                    for item in extracted_dir.iterdir():
                        item.rename(install_path / item.name)
                    extracted_dir.rmdir()
                
                return True
            else:
                self.logger.error("Could not find extracted Java directory")
                return False

        except Exception as e:
            self.logger.error(f"Failed to extract Java archive: {e}")
            return False

    def activate_version(self, version: str, install_path: Path) -> bool:
        """Activate Java version - 需要管理员权限"""
        try:
            if not self.verify_installation(version, install_path):
                self.logger.error(f"Java {version} installation verification failed")
                return False

            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to activate Java version")
                return False

            # 设置系统环境变量 JAVA_HOME
            java_home = str(install_path)
            if not self._set_system_env_var("JAVA_HOME", java_home):
                self.logger.error("Failed to set JAVA_HOME environment variable")
                return False

            # 更新系统PATH
            java_bin = install_path / "bin"
            if not self._add_to_system_path(str(java_bin)):
                self.logger.error("Failed to add Java bin to system PATH")
                return False

            self.logger.info(f"Successfully activated Java {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to activate Java {version}: {e}")
            return False

    def deactivate_version(self, version: str, install_path: Path) -> bool:
        """Deactivate Java version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to deactivate Java version")
                return False

            # 从系统PATH中移除Java bin目录
            java_bin = install_path / "bin"
            if not self._remove_from_system_path(str(java_bin)):
                self.logger.warning("Failed to remove Java bin from system PATH")

            # 清除JAVA_HOME环境变量
            if not self._remove_system_env_var("JAVA_HOME"):
                self.logger.warning("Failed to remove JAVA_HOME environment variable")

            self.logger.info(f"Successfully deactivated Java {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deactivate Java {version}: {e}")
            return False

    def uninstall_version(self, version: str, install_path: Path) -> bool:
        """Uninstall Java version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to uninstall Java version")
                return False

            # 先停用版本
            self.deactivate_version(version, install_path)

            # 删除安装目录由manager处理
            self.logger.info(f"Java {version} marked for uninstallation")
            return True

        except Exception as e:
            self.logger.error(f"Failed to uninstall Java {version}: {e}")
            return False

    def get_installed_versions(self, base_path: Path) -> List[InstallationInfo]:
        """Get installed Java versions"""
        installed = []
        
        if not base_path.exists():
            return installed

        for version_dir in base_path.iterdir():
            if version_dir.is_dir():
                if self.verify_installation(version_dir.name, version_dir):
                    installed.append(InstallationInfo(
                        language="java",
                        version=version_dir.name,
                        install_path=version_dir
                    ))
        
        return installed

    def verify_installation(self, version: str, install_path: Path) -> bool:
        """Verify Java installation"""
        # Check for java executable
        java_exe = "java.exe" if platform.system() == "Windows" else "java"
        java_path = install_path / "bin" / java_exe
        
        return java_path.exists() and java_path.is_file()

    def get_version_info(self, version: str) -> Optional[LanguageVersion]:
        """Get information about a specific Java version"""
        try:
            version_int = int(version)
            return self._get_version_details(version_int)
        except ValueError:
            self.logger.error(f"Invalid Java version format: {version}")
            return None

    def get_executable_path(self, version: str, install_path: Path) -> Optional[Path]:
        """Get path to Java executable"""
        java_exe = "java.exe" if platform.system() == "Windows" else "java"
        java_path = install_path / "bin" / java_exe
        
        if java_path.exists():
            return java_path
        return None

    def get_environment_variables(self, version: str, install_path: Path) -> Dict[str, str]:
        """Get environment variables for Java"""
        return {
            "JAVA_HOME": str(install_path)
        }

    def _check_admin_privileges(self) -> bool:
        """检查是否有管理员权限"""
        from ..utils.admin import is_admin
        return is_admin()

    def _set_system_env_var(self, name: str, value: str) -> bool:
        """设置系统环境变量"""
        from ..utils.admin import set_system_env_var
        return set_system_env_var(name, value)

    def _remove_system_env_var(self, name: str) -> bool:
        """移除系统环境变量"""
        from ..utils.admin import remove_system_env_var
        return remove_system_env_var(name)

    def _add_to_system_path(self, path: str) -> bool:
        """添加路径到系统PATH"""
        from ..utils.admin import add_to_system_path
        return add_to_system_path(path)

    def _remove_from_system_path(self, path: str) -> bool:
        """从系统PATH中移除路径"""
        from ..utils.admin import remove_from_system_path
        return remove_from_system_path(path)
