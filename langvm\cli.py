"""
Command line interface for LangVM
"""

import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.text import Text

from .core.manager import LanguageManager
from .core.config import Config
from .utils.logger import get_logger, setup_file_logging


console = Console()
logger = get_logger(__name__)


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--config', '-c', type=click.Path(), help='Path to config file')
@click.pass_context
def cli(ctx, verbose, config):
    """LangVM - Universal Language Version Manager"""
    ctx.ensure_object(dict)
    
    # Set up logging
    if verbose:
        logger.setLevel('DEBUG')
    
    # Initialize config
    config_path = Path(config) if config else None
    ctx.obj['config'] = Config(config_path)
    
    # Set up file logging
    log_file = ctx.obj['config'].data_dir / "langvm.log"
    setup_file_logging(log_file, "DEBUG" if verbose else "INFO")
    
    # Initialize manager
    ctx.obj['manager'] = LanguageManager(ctx.obj['config'])


@cli.command()
@click.pass_context
def languages(ctx):
    """List supported languages"""
    manager = ctx.obj['manager']
    supported = manager.get_supported_languages()
    
    console.print("\n[bold blue]Supported Languages:[/bold blue]")
    for lang in sorted(supported):
        console.print(f"  • {lang}")
    console.print()


@cli.command()
@click.argument('language')
@click.option('--all', '-a', is_flag=True, help='Show all versions including pre-releases')
@click.pass_context
def list(ctx, language, all):
    """List available versions for a language"""
    manager = ctx.obj['manager']
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Fetching {language} versions...", total=None)
            versions = manager.list_available_versions(language)
        
        if not versions:
            console.print(f"[red]No versions found for {language}[/red]")
            return
        
        table = Table(title=f"Available {language.title()} Versions")
        table.add_column("Version", style="cyan")
        table.add_column("Type", style="green")
        table.add_column("Status", style="yellow")
        
        for version in versions:
            type_info = "LTS" if version.is_lts else "Regular"
            status_info = "Latest" if version.is_latest else ""
            
            table.add_row(version.version, type_info, status_info)
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('language')
@click.argument('version')
@click.option('--force', '-f', is_flag=True, help='Force reinstall if already installed')
@click.pass_context
def install(ctx, language, version, force):
    """Install a specific version of a language"""
    manager = ctx.obj['manager']
    
    try:
        # Check if already installed
        if not force:
            installed = manager.list_installed_versions(language)
            if any(v.version == version for v in installed):
                console.print(f"[yellow]{language} {version} is already installed[/yellow]")
                return
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Installing {language} {version}...", total=None)
            success = manager.install_version(language, version)
        
        if success:
            console.print(f"[green]✓ Successfully installed {language} {version}[/green]")
        else:
            console.print(f"[red]✗ Failed to install {language} {version}[/red]")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('language')
@click.argument('version')
@click.pass_context
def uninstall(ctx, language, version):
    """Uninstall a specific version of a language"""
    manager = ctx.obj['manager']
    
    try:
        # Confirm uninstallation
        if not click.confirm(f"Are you sure you want to uninstall {language} {version}?"):
            return
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Uninstalling {language} {version}...", total=None)
            success = manager.uninstall_version(language, version)
        
        if success:
            console.print(f"[green]✓ Successfully uninstalled {language} {version}[/green]")
        else:
            console.print(f"[red]✗ Failed to uninstall {language} {version}[/red]")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('language')
@click.argument('version')
@click.pass_context
def use(ctx, language, version):
    """Switch to a specific version of a language"""
    manager = ctx.obj['manager']
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Activating {language} {version}...", total=None)
            success = manager.activate_version(language, version)
        
        if success:
            console.print(f"[green]✓ Now using {language} {version}[/green]")
            
            # Show environment setup instructions
            env_manager = manager.env_manager
            shell_script = env_manager.generate_shell_script("bash")
            
            console.print("\n[bold yellow]Environment Setup:[/bold yellow]")
            console.print("To use this version in your current shell, run:")
            console.print(f"[cyan]eval \"$(langvm env {language})\"[/cyan]")
            
        else:
            console.print(f"[red]✗ Failed to activate {language} {version}[/red]")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('language', required=False)
@click.pass_context
def installed(ctx, language):
    """List installed versions"""
    manager = ctx.obj['manager']
    
    try:
        if language:
            versions = manager.list_installed_versions(language)
            title = f"Installed {language.title()} Versions"
        else:
            versions = manager.list_installed_versions()
            title = "All Installed Versions"
        
        if not versions:
            console.print(f"[yellow]No installed versions found[/yellow]")
            return
        
        table = Table(title=title)
        table.add_column("Language", style="cyan")
        table.add_column("Version", style="green")
        table.add_column("Status", style="yellow")
        table.add_column("Path", style="dim")
        
        for version in versions:
            status = "Active" if version.is_active else ""
            table.add_row(
                version.language,
                version.version,
                status,
                str(version.install_path)
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('language', required=False)
@click.pass_context
def current(ctx, language):
    """Show currently active version(s)"""
    manager = ctx.obj['manager']
    
    try:
        if language:
            current_version = manager.get_current_version(language)
            if current_version:
                console.print(f"[green]{language}: {current_version.version}[/green]")
            else:
                console.print(f"[yellow]No active version for {language}[/yellow]")
        else:
            active_versions = manager.env_manager.get_all_active_versions()
            if active_versions:
                table = Table(title="Currently Active Versions")
                table.add_column("Language", style="cyan")
                table.add_column("Version", style="green")
                table.add_column("Path", style="dim")
                
                for version in active_versions:
                    table.add_row(
                        version.language,
                        version.version,
                        str(version.install_path)
                    )
                
                console.print(table)
            else:
                console.print("[yellow]No active versions[/yellow]")
                
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('language', required=False)
@click.option('--shell', default='bash', help='Shell type (bash, zsh, powershell)')
@click.pass_context
def env(ctx, language, shell):
    """Generate environment setup script"""
    manager = ctx.obj['manager']
    
    try:
        if language:
            current_version = manager.get_current_version(language)
            if not current_version:
                console.print(f"[red]No active version for {language}[/red]")
                sys.exit(1)
        
        script = manager.env_manager.generate_shell_script(shell)
        console.print(script, highlight=False)
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.pass_context
def config(ctx):
    """Show configuration information"""
    config = ctx.obj['config']
    
    panel_content = f"""
[bold]Configuration File:[/bold] {config.config_file}
[bold]Install Directory:[/bold] {config.get_install_dir()}
[bold]Cache Directory:[/bold] {config.get_cache_dir()}
[bold]Temp Directory:[/bold] {config.get_temp_dir()}
"""
    
    console.print(Panel(panel_content.strip(), title="LangVM Configuration"))


def main():
    """Main entry point"""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[red]Unexpected error: {e}[/red]")
        logger.exception("Unexpected error in CLI")
        sys.exit(1)


if __name__ == '__main__':
    main()
