# Contributing to LangVM

Thank you for your interest in contributing to LangVM! This document provides guidelines and information for contributors.

## Getting Started

### Prerequisites

- Python 3.8 or higher
- Git
- Basic knowledge of Python and command-line tools

### Setting up the Development Environment

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/yourusername/langvm.git
   cd langvm
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -e .
   ```

4. **Run tests to ensure everything works**
   ```bash
   pytest
   ```

## Development Guidelines

### Code Style

- Follow PEP 8 Python style guidelines
- Use type hints where appropriate
- Write docstrings for all public functions and classes
- Keep line length under 100 characters

### Code Formatting

We use the following tools for code formatting and linting:

```bash
# Format code
black langvm/

# Check code style
flake8 langvm/

# Type checking
mypy langvm/
```

### Testing

- Write tests for all new functionality
- Ensure all tests pass before submitting a PR
- Aim for good test coverage
- Use descriptive test names

```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=langvm

# Run specific test file
pytest tests/test_config.py
```

## Contributing Process

### 1. Create an Issue

Before starting work on a new feature or bug fix, please create an issue to discuss:
- The problem you're trying to solve
- Your proposed solution
- Any potential breaking changes

### 2. Create a Branch

Create a new branch for your work:
```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/your-bug-fix
```

### 3. Make Changes

- Write clean, well-documented code
- Add tests for new functionality
- Update documentation if needed
- Follow the existing code style

### 4. Test Your Changes

```bash
# Run tests
pytest

# Test the CLI manually
langvm --help
langvm languages
```

### 5. Commit Your Changes

Write clear, descriptive commit messages:
```bash
git add .
git commit -m "Add support for Ruby language adapter

- Implement RubyAdapter class
- Add Ruby version detection
- Update documentation
- Add tests for Ruby adapter"
```

### 6. Submit a Pull Request

1. Push your branch to your fork
2. Create a pull request against the main repository
3. Fill out the PR template with:
   - Description of changes
   - Testing performed
   - Any breaking changes

## Adding New Language Adapters

To add support for a new programming language:

### 1. Create the Adapter Class

Create a new file `langvm/adapters/your_language_adapter.py`:

```python
from ..core.base import LanguageAdapter, LanguageVersion, InstallationInfo

class YourLanguageAdapter(LanguageAdapter):
    def get_language_name(self) -> str:
        return "yourlanguage"
    
    def list_available_versions(self) -> List[LanguageVersion]:
        # Implement version discovery
        pass
    
    def download_version(self, version: str, install_path: Path) -> bool:
        # Implement download and installation
        pass
    
    # Implement other required methods...
```

### 2. Register the Adapter

Add your adapter to `langvm/adapters/__init__.py`:

```python
from .your_language_adapter import YourLanguageAdapter

def get_all_adapters() -> List[Type[LanguageAdapter]]:
    return [
        # ... existing adapters
        YourLanguageAdapter,
    ]
```

### 3. Add Tests

Create tests in `tests/test_your_language_adapter.py`:

```python
from langvm.adapters.your_language_adapter import YourLanguageAdapter

class TestYourLanguageAdapter:
    def test_get_language_name(self):
        adapter = YourLanguageAdapter({})
        assert adapter.get_language_name() == "yourlanguage"
    
    # Add more tests...
```

### 4. Update Documentation

- Add the language to the README.md supported languages list
- Update any relevant documentation

## Reporting Issues

When reporting issues, please include:

- **Environment information**: OS, Python version, LangVM version
- **Steps to reproduce**: Clear steps to reproduce the issue
- **Expected behavior**: What you expected to happen
- **Actual behavior**: What actually happened
- **Error messages**: Any error messages or stack traces
- **Additional context**: Any other relevant information

## Feature Requests

For feature requests, please:

1. Check if a similar feature request already exists
2. Clearly describe the feature and its use case
3. Explain why this feature would be valuable
4. Consider providing a basic implementation plan

## Code of Conduct

Please be respectful and constructive in all interactions. We want to maintain a welcoming environment for all contributors.

## Questions?

If you have questions about contributing, feel free to:
- Open an issue with the "question" label
- Start a discussion in the repository
- Contact the maintainers

Thank you for contributing to LangVM!
