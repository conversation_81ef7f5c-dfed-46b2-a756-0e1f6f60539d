# LangVM Makefile

.PHONY: help install install-dev test test-cov lint format clean build upload docs setup

# Default target
help:
	@echo "LangVM Development Commands:"
	@echo ""
	@echo "  install      Install LangVM for production use"
	@echo "  install-dev  Install LangVM for development"
	@echo "  test         Run tests"
	@echo "  test-cov     Run tests with coverage report"
	@echo "  lint         Run linting checks"
	@echo "  format       Format code with black"
	@echo "  clean        Clean build artifacts"
	@echo "  build        Build distribution packages"
	@echo "  upload       Upload to PyPI (maintainers only)"
	@echo "  docs         Generate documentation"
	@echo "  setup        Run setup script for current platform"
	@echo ""

# Installation targets
install:
	pip install .

install-dev:
	pip install -e .
	pip install -r requirements.txt

# Testing targets
test:
	pytest

test-cov:
	pytest --cov=langvm --cov-report=html --cov-report=term

# Code quality targets
lint:
	flake8 langvm/
	mypy langvm/

format:
	black langvm/ tests/
	isort langvm/ tests/

# Build targets
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python setup.py sdist bdist_wheel

upload: build
	twine upload dist/*

# Documentation
docs:
	@echo "Documentation generation not implemented yet"

# Platform-specific setup
setup:
ifeq ($(OS),Windows_NT)
	powershell -ExecutionPolicy Bypass -File scripts/setup.ps1
else
	bash scripts/setup.sh
endif

# Development helpers
check: lint test
	@echo "All checks passed!"

dev-setup: install-dev
	@echo "Development environment ready!"
	@echo "Try: langvm --help"
