"""
Tests for configuration management
"""

import pytest
import tempfile
from pathlib import Path

from langvm.core.config import Config


class TestConfig:
    """Test configuration management"""
    
    def test_default_config(self):
        """Test default configuration values"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            config = Config(config_file)
            
            # Test default values
            assert config.get("download.timeout") == 300
            assert config.get("download.retries") == 3
            assert config.get("proxy.enabled") is False
            
    def test_config_get_set(self):
        """Test getting and setting configuration values"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            config = Config(config_file)
            
            # Test setting and getting values
            config.set("test.value", "hello")
            assert config.get("test.value") == "hello"
            
            # Test nested values
            config.set("test.nested.value", 42)
            assert config.get("test.nested.value") == 42
            
    def test_config_save_load(self):
        """Test saving and loading configuration"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            
            # Create and modify config
            config1 = Config(config_file)
            config1.set("test.value", "saved")
            config1.save()
            
            # Load config in new instance
            config2 = Config(config_file)
            assert config2.get("test.value") == "saved"
            
    def test_get_install_dir(self):
        """Test getting installation directories"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            config = Config(config_file)
            
            # Test base install dir
            base_dir = config.get_install_dir()
            assert isinstance(base_dir, Path)
            
            # Test language-specific install dir
            java_dir = config.get_install_dir("java")
            assert java_dir.name == "java"
            assert java_dir.parent == base_dir
            
    def test_get_mirror_url(self):
        """Test getting mirror URLs"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            config = Config(config_file)
            
            # Test existing mirror
            java_mirror = config.get_mirror_url("java")
            assert "adoptium" in java_mirror.lower()
            
            # Test non-existing mirror
            unknown_mirror = config.get_mirror_url("unknown")
            assert unknown_mirror == ""
            
    def test_get_language_config(self):
        """Test getting language-specific configuration"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            config = Config(config_file)
            
            # Test existing language config
            java_config = config.get_language_config("java")
            assert isinstance(java_config, dict)
            assert "default_distribution" in java_config
            
            # Test non-existing language config
            unknown_config = config.get_language_config("unknown")
            assert unknown_config == {}
            
    def test_get_proxy_config(self):
        """Test getting proxy configuration"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            config = Config(config_file)
            
            proxy_config = config.get_proxy_config()
            assert isinstance(proxy_config, dict)
            assert "enabled" in proxy_config
            assert proxy_config["enabled"] is False
            
    def test_get_download_config(self):
        """Test getting download configuration"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            config = Config(config_file)
            
            download_config = config.get_download_config()
            assert isinstance(download_config, dict)
            assert "timeout" in download_config
            assert "retries" in download_config
            assert download_config["timeout"] == 300
