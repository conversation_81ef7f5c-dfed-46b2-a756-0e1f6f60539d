#!/bin/bash
# LangVM Environment Setup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect shell
detect_shell() {
    if [ -n "$ZSH_VERSION" ]; then
        echo "zsh"
    elif [ -n "$BASH_VERSION" ]; then
        echo "bash"
    else
        echo "unknown"
    fi
}

# Get shell config file
get_shell_config() {
    local shell_type="$1"
    case "$shell_type" in
        "bash")
            if [ -f "$HOME/.bashrc" ]; then
                echo "$HOME/.bashrc"
            elif [ -f "$HOME/.bash_profile" ]; then
                echo "$HOME/.bash_profile"
            else
                echo "$HOME/.bashrc"
            fi
            ;;
        "zsh")
            echo "$HOME/.zshrc"
            ;;
        *)
            echo "$HOME/.profile"
            ;;
    esac
}

# Add LangVM to shell config
setup_shell_integration() {
    local shell_type=$(detect_shell)
    local config_file=$(get_shell_config "$shell_type")
    
    print_info "Setting up shell integration for $shell_type"
    print_info "Config file: $config_file"
    
    # Check if already configured
    if grep -q "langvm" "$config_file" 2>/dev/null; then
        print_warning "LangVM already configured in $config_file"
        return 0
    fi
    
    # Add LangVM configuration
    cat >> "$config_file" << 'EOF'

# LangVM Configuration
export PATH="$HOME/.local/bin:$PATH"

# LangVM environment function
langvm_env() {
    if command -v langvm >/dev/null 2>&1; then
        eval "$(langvm env)"
    fi
}

# Auto-load LangVM environment
langvm_env

EOF
    
    print_success "Added LangVM configuration to $config_file"
    print_info "Please restart your shell or run: source $config_file"
}

# Install LangVM
install_langvm() {
    print_info "Installing LangVM..."
    
    # Check if Python is available
    if ! command -v python3 >/dev/null 2>&1; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check if pip is available
    if ! command -v pip3 >/dev/null 2>&1; then
        print_error "pip3 is required but not installed"
        exit 1
    fi
    
    # Install LangVM
    pip3 install --user .
    
    print_success "LangVM installed successfully"
}

# Main setup function
main() {
    print_info "Starting LangVM setup..."
    
    # Check if we're in the right directory
    if [ ! -f "setup.py" ]; then
        print_error "Please run this script from the LangVM root directory"
        exit 1
    fi
    
    # Install LangVM
    install_langvm
    
    # Setup shell integration
    setup_shell_integration
    
    print_success "LangVM setup completed!"
    print_info "You can now use 'langvm' command to manage language versions"
    print_info "Try: langvm languages"
}

# Run main function
main "$@"
