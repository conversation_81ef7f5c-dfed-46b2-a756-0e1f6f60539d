"""
Configuration management for LangVM
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from platformdirs import user_config_dir, user_data_dir


class Config:
    """Configuration manager for LangVM"""

    def __init__(self, config_file: Optional[Path] = None):
        self.config_dir = Path(user_config_dir("langvm"))
        self.data_dir = Path(user_data_dir("langvm"))
        self.config_file = config_file or self.config_dir / "config.yaml"
        
        # Create directories if they don't exist
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self._config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        if not self.config_file.exists():
            return self._get_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
                # Merge with defaults to ensure all keys exist
                default_config = self._get_default_config()
                default_config.update(config)
                return default_config
        except Exception as e:
            print(f"Warning: Failed to load config file: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "install_dir": str(self.data_dir / "versions"),
            "temp_dir": str(self.data_dir / "temp"),
            "cache_dir": str(self.data_dir / "cache"),
            "mirrors": {
                "java": "https://api.adoptium.net/v3/",
                "python": "https://www.python.org/ftp/python/",
                "go": "https://golang.org/dl/",
                "nodejs": "https://nodejs.org/dist/",
                "rust": "https://forge.rust-lang.org/",
            },
            "proxy": {
                "enabled": False,
                "http": "",
                "https": "",
            },
            "download": {
                "timeout": 300,
                "retries": 3,
                "chunk_size": 8192,
            },
            "languages": {
                "java": {
                    "default_distribution": "temurin",
                    "auto_set_java_home": True,
                },
                "python": {
                    "auto_set_python_home": True,
                },
                "go": {
                    "auto_set_goroot": True,
                },
                "nodejs": {
                    "auto_set_node_path": True,
                },
            }
        }

    def save(self):
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, indent=2)
        except Exception as e:
            raise RuntimeError(f"Failed to save config: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value

    def set(self, key: str, value: Any):
        """Set configuration value"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value

    def get_install_dir(self, language: str = None) -> Path:
        """Get installation directory for a language"""
        base_dir = Path(self.get("install_dir"))
        if language:
            return base_dir / language
        return base_dir

    def get_temp_dir(self) -> Path:
        """Get temporary directory"""
        return Path(self.get("temp_dir"))

    def get_cache_dir(self) -> Path:
        """Get cache directory"""
        return Path(self.get("cache_dir"))

    def get_mirror_url(self, language: str) -> str:
        """Get mirror URL for a language"""
        return self.get(f"mirrors.{language}", "")

    def get_language_config(self, language: str) -> Dict[str, Any]:
        """Get configuration for a specific language"""
        return self.get(f"languages.{language}", {})

    def get_proxy_config(self) -> Dict[str, Any]:
        """Get proxy configuration"""
        return self.get("proxy", {})

    def get_download_config(self) -> Dict[str, Any]:
        """Get download configuration"""
        return self.get("download", {})
