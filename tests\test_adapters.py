"""
Tests for language adapters
"""

import pytest
from unittest.mock import Mock, patch
from pathlib import Path

from langvm.adapters.java_adapter import JavaAdapter
from langvm.adapters.python_adapter import PythonAdapter
from langvm.adapters.go_adapter import <PERSON><PERSON><PERSON>pter
from langvm.adapters.nodejs_adapter import NodeJSAdapter


class TestJavaAdapter:
    """Test Java adapter"""
    
    def test_get_language_name(self):
        """Test language name"""
        adapter = JavaAdapter({})
        assert adapter.get_language_name() == "java"
        
    def test_get_os_name(self):
        """Test OS name detection"""
        adapter = JavaAdapter({})
        
        with patch('platform.system') as mock_system:
            mock_system.return_value = "Windows"
            assert adapter._get_os_name() == "windows"
            
            mock_system.return_value = "Darwin"
            assert adapter._get_os_name() == "mac"
            
            mock_system.return_value = "Linux"
            assert adapter._get_os_name() == "linux"
            
    def test_get_architecture(self):
        """Test architecture detection"""
        adapter = JavaAdapter({})
        
        with patch('platform.machine') as mock_machine:
            mock_machine.return_value = "x86_64"
            assert adapter._get_architecture() == "x64"
            
            mock_machine.return_value = "aarch64"
            assert adapter._get_architecture() == "aarch64"
            
            mock_machine.return_value = "i386"
            assert adapter._get_architecture() == "x32"
            
    @patch('requests.get')
    def test_list_available_versions(self, mock_get):
        """Test listing available versions"""
        adapter = JavaAdapter({})
        
        # Mock API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "lts_versions": [11, 17],
            "available_releases": [8, 11, 17, 19]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock head requests for version details
        with patch('requests.head') as mock_head:
            mock_head_response = Mock()
            mock_head_response.status_code = 200
            mock_head_response.url = "https://example.com/java-17.tar.gz"
            mock_head.return_value = mock_head_response
            
            versions = adapter.list_available_versions()
            
            # Should return versions in descending order
            assert len(versions) > 0
            assert all(v.language == "java" for v in versions)
            
    def test_verify_installation(self):
        """Test installation verification"""
        adapter = JavaAdapter({})
        
        with patch('platform.system') as mock_system:
            mock_system.return_value = "Linux"
            
            # Test with existing java executable
            with patch('pathlib.Path.exists') as mock_exists:
                with patch('pathlib.Path.is_file') as mock_is_file:
                    mock_exists.return_value = True
                    mock_is_file.return_value = True
                    
                    assert adapter.verify_installation("17", Path("/test/java/17"))
                    
            # Test with missing java executable
            with patch('pathlib.Path.exists') as mock_exists:
                mock_exists.return_value = False
                
                assert not adapter.verify_installation("17", Path("/test/java/17"))


class TestPythonAdapter:
    """Test Python adapter"""
    
    def test_get_language_name(self):
        """Test language name"""
        adapter = PythonAdapter({})
        assert adapter.get_language_name() == "python"
        
    def test_get_os_name(self):
        """Test OS name detection"""
        adapter = PythonAdapter({})
        
        with patch('platform.system') as mock_system:
            mock_system.return_value = "Windows"
            assert adapter._get_os_name() == "windows"
            
            mock_system.return_value = "Darwin"
            assert adapter._get_os_name() == "macos"
            
            mock_system.return_value = "Linux"
            assert adapter._get_os_name() == "linux"
            
    def test_get_download_url(self):
        """Test download URL generation"""
        adapter = PythonAdapter({})
        
        with patch.object(adapter, '_get_os_name') as mock_os:
            with patch.object(adapter, '_get_architecture') as mock_arch:
                mock_os.return_value = "windows"
                mock_arch.return_value = "x64"
                
                url = adapter._get_download_url("3.11.0")
                assert "python-3.11.0-amd64.exe" in url
                
                mock_os.return_value = "linux"
                url = adapter._get_download_url("3.11.0")
                assert "Python-3.11.0.tgz" in url


class TestGoAdapter:
    """Test Go adapter"""
    
    def test_get_language_name(self):
        """Test language name"""
        adapter = GoAdapter({})
        assert adapter.get_language_name() == "go"
        
    def test_get_os_name(self):
        """Test OS name detection"""
        adapter = GoAdapter({})
        
        with patch('platform.system') as mock_system:
            mock_system.return_value = "Windows"
            assert adapter._get_os_name() == "windows"
            
            mock_system.return_value = "Darwin"
            assert adapter._get_os_name() == "darwin"
            
            mock_system.return_value = "Linux"
            assert adapter._get_os_name() == "linux"
            
    def test_get_architecture(self):
        """Test architecture detection"""
        adapter = GoAdapter({})
        
        with patch('platform.machine') as mock_machine:
            mock_machine.return_value = "x86_64"
            assert adapter._get_architecture() == "amd64"
            
            mock_machine.return_value = "aarch64"
            assert adapter._get_architecture() == "arm64"
            
            mock_machine.return_value = "i386"
            assert adapter._get_architecture() == "386"


class TestNodeJSAdapter:
    """Test Node.js adapter"""
    
    def test_get_language_name(self):
        """Test language name"""
        adapter = NodeJSAdapter({})
        assert adapter.get_language_name() == "nodejs"
        
    def test_get_download_url(self):
        """Test download URL generation"""
        adapter = NodeJSAdapter({})
        
        with patch.object(adapter, '_get_os_name') as mock_os:
            with patch.object(adapter, '_get_architecture') as mock_arch:
                mock_os.return_value = "windows"
                mock_arch.return_value = "x64"
                
                url = adapter._get_download_url("18.0.0")
                assert "node-v18.0.0-win-x64.zip" in url
                
                mock_os.return_value = "linux"
                mock_arch.return_value = "x64"
                
                url = adapter._get_download_url("18.0.0")
                assert "node-v18.0.0-linux-x64.tar.xz" in url
