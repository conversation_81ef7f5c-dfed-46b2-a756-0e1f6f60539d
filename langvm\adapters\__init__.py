"""
Language adapters for LangVM
"""

from typing import List, Type
from ..core.base import LanguageAdapter

# Import all adapters
from .java_adapter import JavaAdapter
from .python_adapter import PythonAdapter
from .go_adapter import GoAdapter
from .nodejs_adapter import Node<PERSON><PERSON><PERSON>pter


def get_all_adapters() -> List[Type[LanguageAdapter]]:
    """Get all available language adapters"""
    return [
        JavaAdapter,
        PythonAdapter,
        GoAdapter,
        NodeJSAdapter,
    ]


__all__ = [
    "JavaAdapter",
    "PythonAdapter", 
    "GoAdapter",
    "NodeJSAdapter",
    "get_all_adapters",
]
