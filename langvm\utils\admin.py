"""
管理员权限相关工具函数
"""

import os
import sys
import platform
import subprocess
from pathlib import Path
from typing import Optional

from .logger import get_logger


logger = get_logger(__name__)


def is_admin() -> bool:
    """检查当前进程是否有管理员权限"""
    try:
        if platform.system() == "Windows":
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        else:
            return os.geteuid() == 0
    except Exception as e:
        logger.warning(f"Failed to check admin privileges: {e}")
        return False


def request_admin_privileges() -> bool:
    """请求管理员权限"""
    if is_admin():
        return True
    
    try:
        if platform.system() == "Windows":
            # Windows: 使用UAC提升权限
            import ctypes
            ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                sys.executable, 
                " ".join(sys.argv), 
                None, 
                1
            )
            return True
        else:
            # Linux/macOS: 使用sudo
            logger.info("Administrator privileges required. Please run with sudo.")
            return False
    except Exception as e:
        logger.error(f"Failed to request admin privileges: {e}")
        return False


def run_as_admin(command: list, cwd: Optional[Path] = None) -> bool:
    """以管理员权限运行命令"""
    try:
        if platform.system() == "Windows":
            # Windows: 使用runas
            cmd = ["runas", "/user:Administrator"] + command
        else:
            # Linux/macOS: 使用sudo
            cmd = ["sudo"] + command
        
        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.debug(f"Successfully executed admin command: {' '.join(command)}")
            return True
        else:
            logger.error(f"Admin command failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run admin command: {e}")
        return False


def set_system_env_var(name: str, value: str) -> bool:
    """设置系统环境变量（需要管理员权限）"""
    try:
        if platform.system() == "Windows":
            import winreg
            with winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
                0,
                winreg.KEY_SET_VALUE
            ) as key:
                winreg.SetValueEx(key, name, 0, winreg.REG_EXPAND_SZ, value)
            
            # 通知系统环境变量已更改
            import ctypes
            ctypes.windll.user32.SendMessageW(0xFFFF, 0x001A, 0, "Environment")
            logger.info(f"Set system environment variable {name}={value}")
            return True
        else:
            # Linux/macOS: 写入到 /etc/environment
            env_file = Path("/etc/environment")
            if env_file.exists():
                # 读取现有内容
                lines = env_file.read_text().splitlines()
                # 移除已存在的同名变量
                lines = [line for line in lines if not line.startswith(f"{name}=")]
                # 添加新变量
                lines.append(f"{name}={value}")
                # 写回文件
                env_file.write_text("\n".join(lines) + "\n")
                logger.info(f"Set system environment variable {name}={value}")
                return True
            return False
    except Exception as e:
        logger.error(f"Failed to set system environment variable {name}: {e}")
        return False


def remove_system_env_var(name: str) -> bool:
    """移除系统环境变量（需要管理员权限）"""
    try:
        if platform.system() == "Windows":
            import winreg
            with winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
                0,
                winreg.KEY_SET_VALUE
            ) as key:
                try:
                    winreg.DeleteValue(key, name)
                    logger.info(f"Removed system environment variable {name}")
                except FileNotFoundError:
                    logger.debug(f"Environment variable {name} not found")
            
            # 通知系统环境变量已更改
            import ctypes
            ctypes.windll.user32.SendMessageW(0xFFFF, 0x001A, 0, "Environment")
            return True
        else:
            # Linux/macOS: 从 /etc/environment 中移除
            env_file = Path("/etc/environment")
            if env_file.exists():
                lines = env_file.read_text().splitlines()
                new_lines = [line for line in lines if not line.startswith(f"{name}=")]
                env_file.write_text("\n".join(new_lines) + "\n")
                logger.info(f"Removed system environment variable {name}")
            return True
    except Exception as e:
        logger.error(f"Failed to remove system environment variable {name}: {e}")
        return False


def add_to_system_path(path: str) -> bool:
    """添加路径到系统PATH（需要管理员权限）"""
    try:
        if platform.system() == "Windows":
            import winreg
            with winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
                0,
                winreg.KEY_READ | winreg.KEY_WRITE
            ) as key:
                try:
                    current_path, _ = winreg.QueryValueEx(key, "Path")
                except FileNotFoundError:
                    current_path = ""
                
                # 检查路径是否已存在
                path_parts = current_path.split(";") if current_path else []
                if path not in path_parts:
                    new_path = f"{path};{current_path}" if current_path else path
                    winreg.SetValueEx(key, "Path", 0, winreg.REG_EXPAND_SZ, new_path)
                    logger.info(f"Added {path} to system PATH")
                else:
                    logger.debug(f"Path {path} already in system PATH")
            
            # 通知系统环境变量已更改
            import ctypes
            ctypes.windll.user32.SendMessageW(0xFFFF, 0x001A, 0, "Environment")
            return True
        else:
            # Linux/macOS: 添加到 /etc/environment
            env_file = Path("/etc/environment")
            if env_file.exists():
                content = env_file.read_text()
                if f'PATH="{path}:$PATH"' not in content:
                    with open(env_file, "a") as f:
                        f.write(f'\nPATH="{path}:$PATH"\n')
                    logger.info(f"Added {path} to system PATH")
                else:
                    logger.debug(f"Path {path} already in system PATH")
            return True
    except Exception as e:
        logger.error(f"Failed to add {path} to system PATH: {e}")
        return False


def remove_from_system_path(path: str) -> bool:
    """从系统PATH中移除路径（需要管理员权限）"""
    try:
        if platform.system() == "Windows":
            import winreg
            with winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
                0,
                winreg.KEY_READ | winreg.KEY_WRITE
            ) as key:
                try:
                    current_path, _ = winreg.QueryValueEx(key, "Path")
                    path_parts = current_path.split(";")
                    new_path_parts = [p for p in path_parts if p != path]
                    new_path = ";".join(new_path_parts)
                    winreg.SetValueEx(key, "Path", 0, winreg.REG_EXPAND_SZ, new_path)
                    logger.info(f"Removed {path} from system PATH")
                except FileNotFoundError:
                    logger.debug("System PATH not found")
            
            # 通知系统环境变量已更改
            import ctypes
            ctypes.windll.user32.SendMessageW(0xFFFF, 0x001A, 0, "Environment")
            return True
        else:
            # Linux/macOS: 从配置文件中移除
            env_file = Path("/etc/environment")
            if env_file.exists():
                lines = env_file.read_text().splitlines()
                new_lines = [line for line in lines if path not in line]
                env_file.write_text("\n".join(new_lines) + "\n")
                logger.info(f"Removed {path} from system PATH")
            return True
    except Exception as e:
        logger.error(f"Failed to remove {path} from system PATH: {e}")
        return False
