# LangVM Default Configuration

# Installation directory for language versions
install_dir: "~/.langvm/versions"

# Temporary directory for downloads
temp_dir: "~/.langvm/temp"

# Cache directory for metadata
cache_dir: "~/.langvm/cache"

# Mirror URLs for faster downloads
mirrors:
  java: "https://api.adoptium.net/v3/"
  python: "https://www.python.org/ftp/python/"
  go: "https://golang.org/dl/"
  nodejs: "https://nodejs.org/dist/"
  rust: "https://forge.rust-lang.org/"

# Proxy configuration
proxy:
  enabled: false
  http: ""
  https: ""

# Download settings
download:
  timeout: 300  # seconds
  retries: 3
  chunk_size: 8192  # bytes

# Language-specific settings
languages:
  java:
    default_distribution: "temurin"
    auto_set_java_home: true
    
  python:
    auto_set_python_home: true
    
  go:
    auto_set_goroot: true
    
  nodejs:
    auto_set_node_path: true
    
  rust:
    auto_set_rust_home: true
