# LangVM - 通用编程语言版本管理器

LangVM 是一个通用的编程语言版本管理工具，可以轻松安装、管理和切换不同版本的热门编程语言，如 Java、Python、Go、Node.js 等。

## 特性

- 🚀 **多语言支持**: 管理 Java、Python、Go、Node.js、Rust 等多种语言
- 📦 **简单安装**: 使用简单命令安装任意版本
- 🔄 **快速切换**: 瞬间切换不同版本
- 🌍 **跨平台**: 支持 Windows、macOS 和 Linux
- ⚙️ **可配置**: 自定义设置和添加新语言适配器
- 🔧 **环境管理**: 自动管理 PATH 和环境变量
- 🇨🇳 **国内优化**: 使用清华大学镜像，下载速度更快

## 安装

### 方法一：从源码安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/langvm.git
cd langvm

# 运行安装脚本
# Windows (PowerShell 管理员模式):
powershell -ExecutionPolicy Bypass -File scripts/setup.ps1

# Linux/macOS:
bash scripts/setup.sh
```

### 方法二：手动安装

```bash
# 安装依赖
pip install -r requirements.txt

# 安装 LangVM
pip install -e .
```

## 快速开始

### 查看支持的语言

```bash
langvm languages
```

### 列出可用版本

```bash
# 列出 Java 可用版本
langvm list java

# 列出 Python 可用版本
langvm list python
```

### 安装语言版本

```bash
# 安装 Java 17 (LTS)
langvm install java 17

# 安装 Python 3.11
langvm install python 3.11.0

# 安装 Go 1.20
langvm install go 1.20.0

# 安装 Node.js 18
langvm install nodejs 18.0.0
```

### 切换版本

```bash
# 用户级别切换 (推荐)
langvm use java 17

# 系统级别切换 (需要管理员权限)
langvm use --system java 17
```

### 激活环境

切换版本后，需要更新 shell 环境：

```bash
# Bash/Zsh
eval "$(langvm env)"

# PowerShell
Invoke-Expression (langvm env --shell powershell)
```

### 查看当前版本

```bash
# 查看所有激活的版本
langvm current

# 查看特定语言的当前版本
langvm current java
```

### 查看已安装版本

```bash
# 查看所有已安装版本
langvm installed

# 查看特定语言的已安装版本
langvm installed java
```

### 卸载版本

```bash
langvm uninstall java 17
```

## 管理员权限

某些操作需要管理员权限：

### 检查权限状态

```bash
langvm admin
```

### 需要管理员权限的操作

- 系统级别的版本切换 (`--system` 选项)
- 设置系统环境变量
- 修改系统 PATH

### 获取管理员权限

**Windows:**
- 以管理员身份运行 PowerShell
- 或使用 `langvm use --system` 自动请求提升权限

**Linux/macOS:**
- 使用 `sudo langvm`
- 或使用 `langvm use --system` 自动请求提升权限

## 配置

### 查看配置

```bash
langvm config
```

### 自定义配置

编辑配置文件 `~/.langvm/config.yaml`：

```yaml
# 自定义安装目录
install_dir: "/opt/langvm/versions"

# 使用国内镜像 (默认已配置)
mirrors:
  java: "https://mirrors.tuna.tsinghua.edu.cn/Adoptium/"
  python: "https://mirrors.tuna.tsinghua.edu.cn/python/"
  go: "https://mirrors.tuna.tsinghua.edu.cn/golang/"
  nodejs: "https://mirrors.tuna.tsinghua.edu.cn/nodejs-release/"

# 代理设置
proxy:
  enabled: true
  http: "http://proxy.company.com:8080"
  https: "https://proxy.company.com:8080"
```

## 支持的语言

| 语言 | 状态 | 说明 |
|------|------|------|
| ☕ **Java** | ✅ 完全支持 | OpenJDK (Temurin), 支持 LTS 版本 |
| 🐍 **Python** | ✅ 完全支持 | CPython 官方版本 |
| 🐹 **Go** | ✅ 完全支持 | 官方发布版本 |
| 📦 **Node.js** | ✅ 完全支持 | 官方版本，包括 LTS |
| 🦀 **Rust** | 🚧 开发中 | 计划支持 |
| 💎 **Ruby** | 🚧 开发中 | 计划支持 |

## 使用示例

### Java 开发

```bash
# 安装多个 Java 版本
langvm install java 8
langvm install java 11
langvm install java 17
langvm install java 21

# 为旧项目切换到 Java 11
langvm use java 11
eval "$(langvm env)"
java -version  # 显示 Java 11

# 为新项目切换到 Java 21
langvm use java 21
eval "$(langvm env)"
java -version  # 显示 Java 21
```

### Python 开发

```bash
# 安装多个 Python 版本
langvm install python 3.9.0
langvm install python 3.10.0
langvm install python 3.11.0

# 使用 Python 3.11 开发新项目
langvm use python 3.11.0
eval "$(langvm env)"
python --version  # 显示 Python 3.11.0

# 创建虚拟环境
python -m venv myproject
# Windows:
myproject\Scripts\activate
# Linux/macOS:
source myproject/bin/activate
```

### 企业环境配置

```bash
# 设置系统级别的 Java 版本 (需要管理员权限)
langvm use --system java 17

# 验证系统配置
java -version
echo $JAVA_HOME  # Linux/macOS
echo $env:JAVA_HOME  # Windows PowerShell
```

## 故障排除

### 版本未找到

```bash
# 检查可用版本
langvm list java

# 确保使用正确的版本格式
langvm install java 17  # 正确
# 不是 java-17 或 jdk-17
```

### 环境未更新

```bash
# 确保运行环境命令
eval "$(langvm env)"

# 或重启 shell
```

### 权限问题

```bash
# 检查权限状态
langvm admin

# 检查安装目录权限
langvm config
```

### 下载失败

```bash
# 检查网络连接
# 检查代理设置
# 使用详细输出重试
langvm -v install java 17
```

## 开发

### 运行测试

```bash
# 安装开发依赖
pip install -r requirements.txt

# 运行测试
pytest

# 运行带覆盖率的测试
pytest --cov=langvm
```

### 代码格式化

```bash
# 格式化代码
black langvm/ tests/

# 检查代码风格
flake8 langvm/

# 类型检查
mypy langvm/
```

## 贡献

欢迎贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 许可证

MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

如有问题或建议，请：
- 提交 Issue
- 发起 Discussion
- 联系维护者

---

**注意**: 本项目专为中国用户优化，使用清华大学开源软件镜像站加速下载。
