"""
Download utilities for LangVM
"""

import os
import hashlib
import requests
from pathlib import Path
from typing import Optional, Dict, Any, Callable
from urllib.parse import urlparse

from .logger import get_logger


class DownloadProgress:
    """Progress tracker for downloads"""
    
    def __init__(self, total_size: int, callback: Optional[Callable[[int, int], None]] = None):
        self.total_size = total_size
        self.downloaded = 0
        self.callback = callback
    
    def update(self, chunk_size: int):
        """Update progress"""
        self.downloaded += chunk_size
        if self.callback:
            self.callback(self.downloaded, self.total_size)
    
    @property
    def percentage(self) -> float:
        """Get download percentage"""
        if self.total_size == 0:
            return 0.0
        return (self.downloaded / self.total_size) * 100


class Downloader:
    """File downloader with progress tracking and verification"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """Create configured requests session"""
        session = requests.Session()
        
        # Set up proxy if configured
        proxy_config = self.config.get_proxy_config()
        if proxy_config.get("enabled"):
            proxies = {}
            if proxy_config.get("http"):
                proxies["http"] = proxy_config["http"]
            if proxy_config.get("https"):
                proxies["https"] = proxy_config["https"]
            session.proxies.update(proxies)
        
        # Set timeout
        download_config = self.config.get_download_config()
        session.timeout = download_config.get("timeout", 300)
        
        return session
    
    def download_file(
        self,
        url: str,
        destination: Path,
        checksum: Optional[str] = None,
        checksum_algorithm: str = "sha256",
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> bool:
        """
        Download a file with progress tracking and verification
        
        Args:
            url: URL to download from
            destination: Path to save the file
            checksum: Expected checksum for verification
            checksum_algorithm: Algorithm for checksum (sha256, md5, etc.)
            progress_callback: Callback function for progress updates
        
        Returns:
            True if download was successful, False otherwise
        """
        try:
            self.logger.info(f"Downloading {url}")
            
            # Create destination directory
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Get download configuration
            download_config = self.config.get_download_config()
            chunk_size = download_config.get("chunk_size", 8192)
            retries = download_config.get("retries", 3)
            
            # Attempt download with retries
            for attempt in range(retries):
                try:
                    response = self.session.get(url, stream=True)
                    response.raise_for_status()
                    
                    # Get file size
                    total_size = int(response.headers.get('content-length', 0))
                    
                    # Set up progress tracking
                    progress = DownloadProgress(total_size, progress_callback)
                    
                    # Download file
                    with open(destination, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:
                                f.write(chunk)
                                progress.update(len(chunk))
                    
                    # Verify checksum if provided
                    if checksum:
                        if not self.verify_checksum(destination, checksum, checksum_algorithm):
                            self.logger.error(f"Checksum verification failed for {destination}")
                            destination.unlink(missing_ok=True)
                            return False
                    
                    self.logger.info(f"Successfully downloaded {destination}")
                    return True
                    
                except requests.RequestException as e:
                    self.logger.warning(f"Download attempt {attempt + 1} failed: {e}")
                    if attempt == retries - 1:
                        raise
                    
        except Exception as e:
            self.logger.error(f"Failed to download {url}: {e}")
            # Clean up partial download
            if destination.exists():
                destination.unlink(missing_ok=True)
            return False
    
    def verify_checksum(
        self,
        file_path: Path,
        expected_checksum: str,
        algorithm: str = "sha256"
    ) -> bool:
        """
        Verify file checksum
        
        Args:
            file_path: Path to the file to verify
            expected_checksum: Expected checksum value
            algorithm: Hash algorithm to use
        
        Returns:
            True if checksum matches, False otherwise
        """
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            
            actual_checksum = hash_obj.hexdigest()
            
            if actual_checksum.lower() == expected_checksum.lower():
                self.logger.debug(f"Checksum verification passed for {file_path}")
                return True
            else:
                self.logger.error(
                    f"Checksum mismatch for {file_path}: "
                    f"expected {expected_checksum}, got {actual_checksum}"
                )
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to verify checksum for {file_path}: {e}")
            return False
    
    def get_file_size(self, url: str) -> Optional[int]:
        """Get file size from URL without downloading"""
        try:
            response = self.session.head(url)
            response.raise_for_status()
            return int(response.headers.get('content-length', 0))
        except Exception as e:
            self.logger.warning(f"Failed to get file size for {url}: {e}")
            return None
    
    def get_filename_from_url(self, url: str) -> str:
        """Extract filename from URL"""
        parsed = urlparse(url)
        filename = os.path.basename(parsed.path)
        
        if not filename:
            # If no filename in path, use last part of URL
            filename = url.split('/')[-1]
        
        return filename or "download"
