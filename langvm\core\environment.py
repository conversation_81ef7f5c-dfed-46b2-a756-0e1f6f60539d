"""
Environment management for LangVM
"""

import os
import json
import platform
from pathlib import Path
from typing import Dict, Optional, List

from .base import InstallationInfo
from .config import Config
from ..utils.logger import get_logger


class EnvironmentManager:
    """Manages environment variables and PATH for different language versions"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.state_file = config.data_dir / "active_versions.json"
        self._active_versions = self._load_active_versions()

    def _load_active_versions(self) -> Dict[str, Dict]:
        """Load currently active versions from state file"""
        if not self.state_file.exists():
            return {}
        
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to load active versions: {e}")
            return {}

    def _save_active_versions(self):
        """Save active versions to state file"""
        try:
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(self._active_versions, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save active versions: {e}")

    def set_active_version(self, language: str, version: str, install_path: Path):
        """Set a version as active for a language"""
        self._active_versions[language] = {
            "version": version,
            "install_path": str(install_path),
            "activated_at": str(Path().cwd())  # Current working directory when activated
        }
        self._save_active_versions()
        
        # Update environment for current session
        self._update_environment(language, version, install_path)

    def unset_active_version(self, language: str):
        """Unset active version for a language"""
        if language in self._active_versions:
            # Clean up environment
            old_info = self._active_versions[language]
            self._cleanup_environment(language, old_info["version"], Path(old_info["install_path"]))
            
            del self._active_versions[language]
            self._save_active_versions()

    def get_active_version(self, language: str) -> Optional[InstallationInfo]:
        """Get currently active version for a language"""
        if language not in self._active_versions:
            return None
        
        info = self._active_versions[language]
        return InstallationInfo(
            language=language,
            version=info["version"],
            install_path=Path(info["install_path"]),
            is_active=True
        )

    def get_all_active_versions(self) -> List[InstallationInfo]:
        """Get all currently active versions"""
        active = []
        for language, info in self._active_versions.items():
            active.append(InstallationInfo(
                language=language,
                version=info["version"],
                install_path=Path(info["install_path"]),
                is_active=True
            ))
        return active

    def _update_environment(self, language: str, version: str, install_path: Path):
        """Update environment variables for a language version"""
        # Get language-specific environment variables
        env_vars = self._get_language_env_vars(language, version, install_path)
        
        # Update PATH
        bin_paths = self._get_bin_paths(language, install_path)
        if bin_paths:
            self._update_path(bin_paths)
        
        # Set language-specific environment variables
        for key, value in env_vars.items():
            os.environ[key] = value
            self.logger.debug(f"Set {key}={value}")

    def _cleanup_environment(self, language: str, version: str, install_path: Path):
        """Clean up environment variables for a language version"""
        # Remove from PATH
        bin_paths = self._get_bin_paths(language, install_path)
        if bin_paths:
            self._remove_from_path(bin_paths)
        
        # Remove language-specific environment variables
        env_vars = self._get_language_env_vars(language, version, install_path)
        for key in env_vars.keys():
            if key in os.environ:
                del os.environ[key]
                self.logger.debug(f"Removed {key}")

    def _get_language_env_vars(self, language: str, version: str, install_path: Path) -> Dict[str, str]:
        """Get environment variables for a specific language"""
        env_vars = {}
        
        if language == "java":
            env_vars["JAVA_HOME"] = str(install_path)
        elif language == "python":
            env_vars["PYTHON_HOME"] = str(install_path)
        elif language == "go":
            env_vars["GOROOT"] = str(install_path)
        elif language == "nodejs":
            env_vars["NODE_PATH"] = str(install_path / "lib" / "node_modules")
        elif language == "rust":
            env_vars["RUSTUP_HOME"] = str(install_path)
            env_vars["CARGO_HOME"] = str(install_path)
        
        return env_vars

    def _get_bin_paths(self, language: str, install_path: Path) -> List[Path]:
        """Get binary paths for a language installation"""
        bin_paths = []
        
        # Common bin directory
        bin_dir = install_path / "bin"
        if bin_dir.exists():
            bin_paths.append(bin_dir)
        
        # Language-specific paths
        if language == "python":
            # Python on Windows
            if platform.system() == "Windows":
                if install_path.exists():
                    bin_paths.append(install_path)
                scripts_dir = install_path / "Scripts"
                if scripts_dir.exists():
                    bin_paths.append(scripts_dir)
        elif language == "nodejs":
            # Node.js on Windows doesn't use bin subdirectory
            if platform.system() == "Windows" and install_path.exists():
                bin_paths.append(install_path)
        elif language == "go":
            # Go binary path
            go_bin = install_path / "bin"
            if go_bin.exists():
                bin_paths.append(go_bin)
        
        return bin_paths

    def _update_path(self, new_paths: List[Path]):
        """Add paths to the beginning of PATH"""
        current_path = os.environ.get("PATH", "")
        path_separator = ";" if platform.system() == "Windows" else ":"
        
        # Convert paths to strings and filter existing ones
        new_path_strs = [str(p) for p in new_paths if p.exists()]
        
        # Remove these paths if they already exist in PATH
        path_parts = current_path.split(path_separator)
        path_parts = [p for p in path_parts if p not in new_path_strs]
        
        # Add new paths to the beginning
        updated_path = path_separator.join(new_path_strs + path_parts)
        os.environ["PATH"] = updated_path
        
        self.logger.debug(f"Updated PATH with: {new_path_strs}")

    def _remove_from_path(self, paths_to_remove: List[Path]):
        """Remove paths from PATH"""
        current_path = os.environ.get("PATH", "")
        path_separator = ";" if platform.system() == "Windows" else ":"
        
        # Convert paths to strings
        remove_path_strs = [str(p) for p in paths_to_remove]
        
        # Filter out the paths to remove
        path_parts = current_path.split(path_separator)
        path_parts = [p for p in path_parts if p not in remove_path_strs]
        
        # Update PATH
        updated_path = path_separator.join(path_parts)
        os.environ["PATH"] = updated_path
        
        self.logger.debug(f"Removed from PATH: {remove_path_strs}")

    def generate_shell_script(self, shell: str = "bash") -> str:
        """Generate shell script to set up environment for active versions"""
        lines = []
        
        if shell in ["bash", "zsh"]:
            lines.append("#!/bin/bash")
            lines.append("# LangVM environment setup")
            lines.append("")
            
            # Set environment variables
            for language, info in self._active_versions.items():
                install_path = Path(info["install_path"])
                version = info["version"]
                
                env_vars = self._get_language_env_vars(language, version, install_path)
                for key, value in env_vars.items():
                    lines.append(f'export {key}="{value}"')
                
                # Update PATH
                bin_paths = self._get_bin_paths(language, install_path)
                for bin_path in bin_paths:
                    if bin_path.exists():
                        lines.append(f'export PATH="{bin_path}:$PATH"')
                
                lines.append("")
        
        elif shell == "powershell":
            lines.append("# LangVM environment setup")
            lines.append("")
            
            # Set environment variables
            for language, info in self._active_versions.items():
                install_path = Path(info["install_path"])
                version = info["version"]
                
                env_vars = self._get_language_env_vars(language, version, install_path)
                for key, value in env_vars.items():
                    lines.append(f'$env:{key} = "{value}"')
                
                # Update PATH
                bin_paths = self._get_bin_paths(language, install_path)
                for bin_path in bin_paths:
                    if bin_path.exists():
                        lines.append(f'$env:PATH = "{bin_path};$env:PATH"')
                
                lines.append("")
        
        return "\n".join(lines)
