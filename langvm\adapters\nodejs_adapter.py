"""
Node.js language adapter for LangVM
"""

import json
import platform
import tarfile
import zipfile
from pathlib import Path
from typing import List, Optional, Dict, Any

import requests

from ..core.base import LanguageAdapter, LanguageVersion, InstallationInfo
from ..utils.logger import get_logger


class NodeJSAdapter(LanguageAdapter):
    """Adapter for managing Node.js versions"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = get_logger(__name__)
        self.api_url = "https://nodejs.org/dist/index.json"
        self.download_base = "https://nodejs.org/dist/"

    def get_language_name(self) -> str:
        return "nodejs"

    def list_available_versions(self) -> List[LanguageVersion]:
        """List available Node.js versions"""
        try:
            response = requests.get(self.api_url, timeout=30)
            response.raise_for_status()
            
            releases = response.json()
            versions = []
            
            for release in releases:
                version = release.get("version", "").lstrip("v")
                if not version:
                    continue
                
                # Get download URL for this version
                download_url = self._get_download_url(version)
                if download_url:
                    version_obj = LanguageVersion(
                        language="nodejs",
                        version=version,
                        url=download_url,
                        metadata={
                            "lts": release.get("lts", False),
                            "security": release.get("security", False),
                            "os": self._get_os_name(),
                            "arch": self._get_architecture()
                        }
                    )
                    
                    # Mark LTS versions
                    if release.get("lts"):
                        version_obj.is_lts = True
                    
                    versions.append(version_obj)
            
            # Sort by version (newest first)
            versions.sort(key=lambda v: tuple(map(int, v.version.split('.'))), reverse=True)
            
            # Mark the latest version
            if versions:
                versions[0].is_latest = True
            
            return versions
            
        except Exception as e:
            self.logger.error(f"Failed to list Node.js versions: {e}")
            return []

    def _get_download_url(self, version: str) -> str:
        """Get download URL for a specific Node.js version"""
        os_name = self._get_os_name()
        arch = self._get_architecture()
        
        if os_name == "windows":
            if arch == "x64":
                filename = f"node-v{version}-win-x64.zip"
            else:
                filename = f"node-v{version}-win-x86.zip"
        elif os_name == "darwin":
            filename = f"node-v{version}-darwin-{arch}.tar.gz"
        else:  # Linux
            filename = f"node-v{version}-linux-{arch}.tar.xz"
        
        return f"{self.download_base}v{version}/{filename}"

    def _get_os_name(self) -> str:
        """Get OS name for Node.js downloads"""
        system = platform.system().lower()
        if system == "windows":
            return "windows"
        elif system == "darwin":
            return "darwin"
        else:
            return "linux"

    def _get_architecture(self) -> str:
        """Get architecture for Node.js downloads"""
        machine = platform.machine().lower()
        if machine in ["x86_64", "amd64"]:
            return "x64"
        elif machine in ["aarch64", "arm64"]:
            return "arm64"
        elif machine in ["x86", "i386", "i686"]:
            return "x86"
        elif machine.startswith("arm"):
            return "armv7l"
        else:
            return "x64"  # Default fallback

    def download_version(self, version: str, install_path: Path) -> bool:
        """Download and install Node.js version"""
        try:
            version_info = self.get_version_info(version)
            if not version_info:
                self.logger.error(f"Version {version} not found")
                return False

            # Create a downloader instance
            from ..utils.downloader import Downloader
            from ..core.config import Config
            downloader = Downloader(Config())

            # Download the archive
            archive_name = downloader.get_filename_from_url(version_info.url)
            archive_path = install_path.parent / f"nodejs-{version}-{archive_name}"
            
            if not downloader.download_file(version_info.url, archive_path):
                return False

            # Extract the archive
            if not self._extract_archive(archive_path, install_path, version):
                return False

            # Clean up archive
            archive_path.unlink(missing_ok=True)
            
            return True

        except Exception as e:
            self.logger.error(f"Failed to download Node.js {version}: {e}")
            return False

    def _extract_archive(self, archive_path: Path, install_path: Path, version: str) -> bool:
        """Extract Node.js archive"""
        try:
            extract_dir = install_path.parent / "temp_extract"
            extract_dir.mkdir(exist_ok=True)
            
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            elif archive_path.suffix.lower() == '.xz':
                # Handle .tar.xz files
                import lzma
                with lzma.open(archive_path, 'rb') as xz_file:
                    with tarfile.open(fileobj=xz_file, mode='r') as tar_ref:
                        tar_ref.extractall(extract_dir)
            else:
                # Assume tar.gz
                with tarfile.open(archive_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(extract_dir)

            # Find the extracted directory
            extracted_dirs = [d for d in extract_dir.iterdir() 
                            if d.is_dir() and 'node' in d.name.lower()]
            
            if extracted_dirs:
                extracted_dir = extracted_dirs[0]
                
                # Move contents to install_path
                install_path.mkdir(parents=True, exist_ok=True)
                import shutil
                for item in extracted_dir.iterdir():
                    dest = install_path / item.name
                    if dest.exists():
                        if dest.is_dir():
                            shutil.rmtree(dest)
                        else:
                            dest.unlink()
                    item.rename(dest)
                
                # Clean up temporary directory
                shutil.rmtree(extract_dir)
                return True
            else:
                self.logger.error("Could not find extracted Node.js directory")
                import shutil
                shutil.rmtree(extract_dir)
                return False

        except Exception as e:
            self.logger.error(f"Failed to extract Node.js archive: {e}")
            return False

    def activate_version(self, version: str, install_path: Path) -> bool:
        """Activate Node.js version - 需要管理员权限"""
        try:
            if not self.verify_installation(version, install_path):
                self.logger.error(f"Node.js {version} installation verification failed")
                return False

            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to activate Node.js version")
                return False

            # 设置系统环境变量
            if not self._set_node_env_vars(install_path):
                self.logger.error("Failed to set Node.js environment variables")
                return False

            # 更新系统PATH
            node_paths = self._get_node_paths(install_path)
            for node_path in node_paths:
                if not self._add_to_system_path(str(node_path)):
                    self.logger.error(f"Failed to add {node_path} to system PATH")
                    return False

            self.logger.info(f"Successfully activated Node.js {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to activate Node.js {version}: {e}")
            return False

    def deactivate_version(self, version: str, install_path: Path) -> bool:
        """Deactivate Node.js version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to deactivate Node.js version")
                return False

            # 从系统PATH中移除Node.js路径
            node_paths = self._get_node_paths(install_path)
            for node_path in node_paths:
                if not self._remove_from_system_path(str(node_path)):
                    self.logger.warning(f"Failed to remove {node_path} from system PATH")

            # 清除Node.js环境变量
            if not self._remove_node_env_vars():
                self.logger.warning("Failed to remove Node.js environment variables")

            self.logger.info(f"Successfully deactivated Node.js {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deactivate Node.js {version}: {e}")
            return False

    def uninstall_version(self, version: str, install_path: Path) -> bool:
        """Uninstall Node.js version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to uninstall Node.js version")
                return False

            # 先停用版本
            self.deactivate_version(version, install_path)

            # 删除安装目录由manager处理
            self.logger.info(f"Node.js {version} marked for uninstallation")
            return True

        except Exception as e:
            self.logger.error(f"Failed to uninstall Node.js {version}: {e}")
            return False

    def get_installed_versions(self, base_path: Path) -> List[InstallationInfo]:
        """Get installed Node.js versions"""
        installed = []
        
        if not base_path.exists():
            return installed

        for version_dir in base_path.iterdir():
            if version_dir.is_dir():
                if self.verify_installation(version_dir.name, version_dir):
                    installed.append(InstallationInfo(
                        language="nodejs",
                        version=version_dir.name,
                        install_path=version_dir
                    ))
        
        return installed

    def verify_installation(self, version: str, install_path: Path) -> bool:
        """Verify Node.js installation"""
        # Check for node executable
        node_exe = "node.exe" if platform.system() == "Windows" else "node"
        
        # Node.js on Windows doesn't use bin subdirectory
        if platform.system() == "Windows":
            node_path = install_path / node_exe
        else:
            node_path = install_path / "bin" / node_exe
        
        return node_path.exists() and node_path.is_file()

    def get_version_info(self, version: str) -> Optional[LanguageVersion]:
        """Get information about a specific Node.js version"""
        download_url = self._get_download_url(version)
        return LanguageVersion(
            language="nodejs",
            version=version,
            url=download_url,
            metadata={
                "os": self._get_os_name(),
                "arch": self._get_architecture()
            }
        )

    def get_executable_path(self, version: str, install_path: Path) -> Optional[Path]:
        """Get path to Node.js executable"""
        node_exe = "node.exe" if platform.system() == "Windows" else "node"
        
        # Node.js on Windows doesn't use bin subdirectory
        if platform.system() == "Windows":
            node_path = install_path / node_exe
        else:
            node_path = install_path / "bin" / node_exe
        
        if node_path.exists():
            return node_path
        return None

    def get_environment_variables(self, version: str, install_path: Path) -> Dict[str, str]:
        """Get environment variables for Node.js"""
        env_vars = {}
        
        # Set NODE_PATH for module resolution
        if platform.system() == "Windows":
            node_modules_path = install_path / "node_modules"
        else:
            node_modules_path = install_path / "lib" / "node_modules"
        
        if node_modules_path.exists():
            env_vars["NODE_PATH"] = str(node_modules_path)
        
        return env_vars

    def _check_admin_privileges(self) -> bool:
        """检查是否有管理员权限"""
        from ..utils.admin import is_admin
        return is_admin()

    def _set_node_env_vars(self, install_path: Path) -> bool:
        """设置Node.js环境变量"""
        from ..utils.admin import set_system_env_var

        # 设置NODE_PATH
        if platform.system() == "Windows":
            node_modules_path = install_path / "node_modules"
        else:
            node_modules_path = install_path / "lib" / "node_modules"

        if node_modules_path.exists():
            return set_system_env_var("NODE_PATH", str(node_modules_path))

        return True

    def _remove_node_env_vars(self) -> bool:
        """移除Node.js环境变量"""
        from ..utils.admin import remove_system_env_var
        return remove_system_env_var("NODE_PATH")

    def _get_node_paths(self, install_path: Path) -> List[Path]:
        """获取Node.js需要添加到PATH的路径"""
        paths = []

        # Node.js主目录
        if platform.system() == "Windows":
            # Windows下Node.js直接在根目录
            if install_path.exists():
                paths.append(install_path)
        else:
            # Linux/macOS下在bin目录
            bin_dir = install_path / "bin"
            if bin_dir.exists():
                paths.append(bin_dir)

        return paths

    def _add_to_system_path(self, path: str) -> bool:
        """添加路径到系统PATH"""
        from ..utils.admin import add_to_system_path
        return add_to_system_path(path)

    def _remove_from_system_path(self, path: str) -> bool:
        """从系统PATH中移除路径"""
        from ..utils.admin import remove_from_system_path
        return remove_from_system_path(path)
