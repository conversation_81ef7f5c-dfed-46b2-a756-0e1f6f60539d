"""
Core language version manager
"""

import os
import shutil
import json
from pathlib import Path
from typing import Dict, List, Optional, Type
from datetime import datetime

from .base import LanguageAdapter, LanguageVersion, InstallationInfo
from .config import Config
from .environment import EnvironmentManager
from ..utils.downloader import Downloader
from ..utils.logger import get_logger


class LanguageManager:
    """Main manager for language versions"""

    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.env_manager = EnvironmentManager(self.config)
        self.downloader = Downloader(self.config)
        self.logger = get_logger(__name__)
        self._adapters: Dict[str, LanguageAdapter] = {}
        self._load_adapters()

    def _load_adapters(self):
        """Load all available language adapters"""
        from ..adapters import get_all_adapters

        for adapter_class in get_all_adapters():
            try:
                # Create a temporary instance to get the language name
                temp_adapter = adapter_class({})
                language_name = temp_adapter.get_language_name()

                # Create the actual adapter with proper config
                adapter = adapter_class(self.config.get_language_config(language_name))
                self._adapters[adapter.language_name] = adapter
                self.logger.debug(f"Loaded adapter for {adapter.language_name}")
            except Exception as e:
                self.logger.error(f"Failed to load adapter {adapter_class}: {e}")

    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages"""
        return list(self._adapters.keys())

    def get_adapter(self, language: str) -> Optional[LanguageAdapter]:
        """Get adapter for a specific language"""
        return self._adapters.get(language)

    def list_available_versions(self, language: str) -> List[LanguageVersion]:
        """List available versions for a language"""
        adapter = self.get_adapter(language)
        if not adapter:
            raise ValueError(f"Unsupported language: {language}")
        
        return adapter.list_available_versions()

    def list_installed_versions(self, language: str = None) -> List[InstallationInfo]:
        """List installed versions"""
        if language:
            adapter = self.get_adapter(language)
            if not adapter:
                raise ValueError(f"Unsupported language: {language}")
            
            install_dir = self.config.get_install_dir(language)
            return adapter.get_installed_versions(install_dir)
        
        # List all installed versions for all languages
        all_installed = []
        for lang in self.get_supported_languages():
            try:
                installed = self.list_installed_versions(lang)
                all_installed.extend(installed)
            except Exception as e:
                self.logger.error(f"Failed to list installed versions for {lang}: {e}")
        
        return all_installed

    def install_version(self, language: str, version: str) -> bool:
        """Install a specific version of a language"""
        adapter = self.get_adapter(language)
        if not adapter:
            raise ValueError(f"Unsupported language: {language}")

        install_dir = self.config.get_install_dir(language)
        version_dir = install_dir / version

        # Check if already installed
        if version_dir.exists() and adapter.verify_installation(version, version_dir):
            self.logger.info(f"{language} {version} is already installed")
            return True

        self.logger.info(f"Installing {language} {version}...")

        try:
            # Create installation directory
            version_dir.mkdir(parents=True, exist_ok=True)

            # Run pre-install hook
            if not adapter.pre_install_hook(version, version_dir):
                self.logger.error("Pre-install hook failed")
                return False

            # Download and install
            if not adapter.download_version(version, version_dir):
                self.logger.error(f"Failed to download {language} {version}")
                # Clean up on failure
                if version_dir.exists():
                    shutil.rmtree(version_dir)
                return False

            # Verify installation
            if not adapter.verify_installation(version, version_dir):
                self.logger.error(f"Installation verification failed for {language} {version}")
                # Clean up on failure
                if version_dir.exists():
                    shutil.rmtree(version_dir)
                return False

            # Run post-install hook
            if not adapter.post_install_hook(version, version_dir):
                self.logger.warning("Post-install hook failed, but installation succeeded")

            # Save installation metadata
            self._save_installation_metadata(language, version, version_dir)

            self.logger.info(f"Successfully installed {language} {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to install {language} {version}: {e}")
            # Clean up on failure
            if version_dir.exists():
                shutil.rmtree(version_dir)
            return False

    def uninstall_version(self, language: str, version: str) -> bool:
        """Uninstall a specific version of a language"""
        adapter = self.get_adapter(language)
        if not adapter:
            raise ValueError(f"Unsupported language: {language}")

        install_dir = self.config.get_install_dir(language)
        version_dir = install_dir / version

        if not version_dir.exists():
            self.logger.warning(f"{language} {version} is not installed")
            return True

        self.logger.info(f"Uninstalling {language} {version}...")

        try:
            # Check if this version is currently active
            current_version = self.get_current_version(language)
            if current_version and current_version.version == version:
                self.logger.info(f"Deactivating {language} {version} before uninstall")
                self.deactivate_version(language, version)

            # Run pre-uninstall hook
            if not adapter.pre_uninstall_hook(version, version_dir):
                self.logger.warning("Pre-uninstall hook failed, continuing with uninstall")

            # Uninstall
            if not adapter.uninstall_version(version, version_dir):
                self.logger.error(f"Failed to uninstall {language} {version}")
                return False

            # Remove directory
            if version_dir.exists():
                shutil.rmtree(version_dir)

            # Run post-uninstall hook
            if not adapter.post_uninstall_hook(version, version_dir):
                self.logger.warning("Post-uninstall hook failed, but uninstall succeeded")

            self.logger.info(f"Successfully uninstalled {language} {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to uninstall {language} {version}: {e}")
            return False

    def activate_version(self, language: str, version: str) -> bool:
        """Activate a specific version of a language"""
        adapter = self.get_adapter(language)
        if not adapter:
            raise ValueError(f"Unsupported language: {language}")

        install_dir = self.config.get_install_dir(language)
        version_dir = install_dir / version

        if not version_dir.exists():
            raise ValueError(f"{language} {version} is not installed")

        if not adapter.verify_installation(version, version_dir):
            raise ValueError(f"{language} {version} installation is corrupted")

        self.logger.info(f"Activating {language} {version}...")

        try:
            # Deactivate current version first
            current = self.get_current_version(language)
            if current and current.version != version:
                self.deactivate_version(language, current.version)

            # Activate new version
            if not adapter.activate_version(version, version_dir):
                self.logger.error(f"Failed to activate {language} {version}")
                return False

            # Update environment
            self.env_manager.set_active_version(language, version, version_dir)

            self.logger.info(f"Successfully activated {language} {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to activate {language} {version}: {e}")
            return False

    def deactivate_version(self, language: str, version: str) -> bool:
        """Deactivate a specific version of a language"""
        adapter = self.get_adapter(language)
        if not adapter:
            raise ValueError(f"Unsupported language: {language}")

        install_dir = self.config.get_install_dir(language)
        version_dir = install_dir / version

        self.logger.info(f"Deactivating {language} {version}...")

        try:
            # Deactivate version
            if not adapter.deactivate_version(version, version_dir):
                self.logger.error(f"Failed to deactivate {language} {version}")
                return False

            # Update environment
            self.env_manager.unset_active_version(language)

            self.logger.info(f"Successfully deactivated {language} {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deactivate {language} {version}: {e}")
            return False

    def get_current_version(self, language: str) -> Optional[InstallationInfo]:
        """Get currently active version for a language"""
        return self.env_manager.get_active_version(language)

    def _save_installation_metadata(self, language: str, version: str, install_path: Path):
        """Save metadata about an installation"""
        metadata = {
            "language": language,
            "version": version,
            "installed_at": datetime.now().isoformat(),
            "install_path": str(install_path),
        }
        
        metadata_file = install_path / ".langvm_metadata.json"
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2)
        except Exception as e:
            self.logger.warning(f"Failed to save installation metadata: {e}")
