"""
Base classes and interfaces for language adapters
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from pathlib import Path


@dataclass
class LanguageVersion:
    """Represents a version of a programming language"""
    language: str
    version: str
    url: str
    checksum: Optional[str] = None
    is_lts: bool = False
    is_latest: bool = False
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class InstallationInfo:
    """Information about an installed language version"""
    language: str
    version: str
    install_path: Path
    is_active: bool = False
    installed_at: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LanguageAdapter(ABC):
    """Abstract base class for language adapters"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.language_name = self.get_language_name()

    @abstractmethod
    def get_language_name(self) -> str:
        """Return the name of the language this adapter handles"""
        pass

    @abstractmethod
    def list_available_versions(self) -> List[LanguageVersion]:
        """List all available versions for this language"""
        pass

    @abstractmethod
    def download_version(self, version: str, install_path: Path) -> bool:
        """Download and install a specific version"""
        pass

    @abstractmethod
    def activate_version(self, version: str, install_path: Path) -> bool:
        """Activate a specific version (set up environment)"""
        pass

    @abstractmethod
    def deactivate_version(self, version: str, install_path: Path) -> bool:
        """Deactivate a specific version"""
        pass

    @abstractmethod
    def uninstall_version(self, version: str, install_path: Path) -> bool:
        """Uninstall a specific version"""
        pass

    @abstractmethod
    def get_installed_versions(self, base_path: Path) -> List[InstallationInfo]:
        """Get list of installed versions"""
        pass

    @abstractmethod
    def verify_installation(self, version: str, install_path: Path) -> bool:
        """Verify that a version is properly installed"""
        pass

    @abstractmethod
    def get_version_info(self, version: str) -> Optional[LanguageVersion]:
        """Get detailed information about a specific version"""
        pass

    def get_executable_path(self, version: str, install_path: Path) -> Optional[Path]:
        """Get the path to the main executable for this version"""
        # Default implementation - can be overridden by specific adapters
        return None

    def get_environment_variables(self, version: str, install_path: Path) -> Dict[str, str]:
        """Get environment variables needed for this version"""
        # Default implementation - can be overridden by specific adapters
        return {}

    def pre_install_hook(self, version: str, install_path: Path) -> bool:
        """Hook called before installation"""
        return True

    def post_install_hook(self, version: str, install_path: Path) -> bool:
        """Hook called after installation"""
        return True

    def pre_uninstall_hook(self, version: str, install_path: Path) -> bool:
        """Hook called before uninstallation"""
        return True

    def post_uninstall_hook(self, version: str, install_path: Path) -> bool:
        """Hook called after uninstallation"""
        return True
