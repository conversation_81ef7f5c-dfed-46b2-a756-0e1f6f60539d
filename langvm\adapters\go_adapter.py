"""
Go language adapter for LangVM
"""

import json
import platform
import tarfile
import zipfile
from pathlib import Path
from typing import List, Optional, Dict, Any

import requests

from ..core.base import LanguageAdapter, LanguageVersion, InstallationInfo
from ..utils.logger import get_logger


class GoAdapter(LanguageAdapter):
    """Adapter for managing Go versions"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = get_logger(__name__)
        self.api_url = "https://golang.org/dl/?mode=json"
        self.download_base = "https://golang.org/dl/"

    def get_language_name(self) -> str:
        return "go"

    def list_available_versions(self) -> List[LanguageVersion]:
        """List available Go versions"""
        try:
            response = requests.get(self.api_url, timeout=30)
            response.raise_for_status()
            
            releases = response.json()
            versions = []
            
            for release in releases:
                version = release.get("version", "").replace("go", "")
                if not version:
                    continue
                
                # Find the appropriate file for current OS/arch
                download_url = self._get_download_url_from_release(release)
                if download_url:
                    version_obj = LanguageVersion(
                        language="go",
                        version=version,
                        url=download_url,
                        metadata={
                            "stable": release.get("stable", False),
                            "os": self._get_os_name(),
                            "arch": self._get_architecture()
                        }
                    )
                    
                    # Mark stable versions
                    if release.get("stable"):
                        version_obj.is_lts = True
                    
                    versions.append(version_obj)
            
            # Mark the latest stable version
            stable_versions = [v for v in versions if v.is_lts]
            if stable_versions:
                stable_versions[0].is_latest = True
            
            return versions
            
        except Exception as e:
            self.logger.error(f"Failed to list Go versions: {e}")
            return []

    def _get_download_url_from_release(self, release: dict) -> Optional[str]:
        """Get download URL for current OS/arch from release data"""
        files = release.get("files", [])
        os_name = self._get_os_name()
        arch = self._get_architecture()
        
        for file_info in files:
            if (file_info.get("os") == os_name and 
                file_info.get("arch") == arch and
                file_info.get("kind") == "archive"):
                return f"{self.download_base}{file_info.get('filename')}"
        
        return None

    def _get_os_name(self) -> str:
        """Get OS name for Go downloads"""
        system = platform.system().lower()
        if system == "windows":
            return "windows"
        elif system == "darwin":
            return "darwin"
        else:
            return "linux"

    def _get_architecture(self) -> str:
        """Get architecture for Go downloads"""
        machine = platform.machine().lower()
        if machine in ["x86_64", "amd64"]:
            return "amd64"
        elif machine in ["aarch64", "arm64"]:
            return "arm64"
        elif machine in ["x86", "i386", "i686"]:
            return "386"
        elif machine.startswith("arm"):
            return "armv6l"
        else:
            return "amd64"  # Default fallback

    def download_version(self, version: str, install_path: Path) -> bool:
        """Download and install Go version"""
        try:
            version_info = self.get_version_info(version)
            if not version_info:
                self.logger.error(f"Version {version} not found")
                return False

            # Create a downloader instance
            from ..utils.downloader import Downloader
            from ..core.config import Config
            downloader = Downloader(Config())

            # Download the archive
            archive_name = downloader.get_filename_from_url(version_info.url)
            archive_path = install_path.parent / f"go-{version}-{archive_name}"
            
            if not downloader.download_file(version_info.url, archive_path):
                return False

            # Extract the archive
            if not self._extract_archive(archive_path, install_path):
                return False

            # Clean up archive
            archive_path.unlink(missing_ok=True)
            
            return True

        except Exception as e:
            self.logger.error(f"Failed to download Go {version}: {e}")
            return False

    def _extract_archive(self, archive_path: Path, install_path: Path) -> bool:
        """Extract Go archive"""
        try:
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(install_path.parent)
            else:
                # Assume tar.gz
                with tarfile.open(archive_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(install_path.parent)

            # Go archives extract to a 'go' directory
            extracted_go_dir = install_path.parent / "go"
            
            if extracted_go_dir.exists():
                # Move contents to install_path
                if not install_path.exists():
                    extracted_go_dir.rename(install_path)
                else:
                    # Move contents
                    import shutil
                    for item in extracted_go_dir.iterdir():
                        dest = install_path / item.name
                        if dest.exists():
                            if dest.is_dir():
                                shutil.rmtree(dest)
                            else:
                                dest.unlink()
                        item.rename(dest)
                    extracted_go_dir.rmdir()
                
                return True
            else:
                self.logger.error("Could not find extracted Go directory")
                return False

        except Exception as e:
            self.logger.error(f"Failed to extract Go archive: {e}")
            return False

    def activate_version(self, version: str, install_path: Path) -> bool:
        """Activate Go version - 需要管理员权限"""
        try:
            if not self.verify_installation(version, install_path):
                self.logger.error(f"Go {version} installation verification failed")
                return False

            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to activate Go version")
                return False

            # 设置系统环境变量
            if not self._set_go_env_vars(install_path):
                self.logger.error("Failed to set Go environment variables")
                return False

            # 更新系统PATH
            go_paths = self._get_go_paths(install_path)
            for go_path in go_paths:
                if not self._add_to_system_path(str(go_path)):
                    self.logger.error(f"Failed to add {go_path} to system PATH")
                    return False

            self.logger.info(f"Successfully activated Go {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to activate Go {version}: {e}")
            return False

    def deactivate_version(self, version: str, install_path: Path) -> bool:
        """Deactivate Go version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to deactivate Go version")
                return False

            # 从系统PATH中移除Go路径
            go_paths = self._get_go_paths(install_path)
            for go_path in go_paths:
                if not self._remove_from_system_path(str(go_path)):
                    self.logger.warning(f"Failed to remove {go_path} from system PATH")

            # 清除Go环境变量
            if not self._remove_go_env_vars():
                self.logger.warning("Failed to remove Go environment variables")

            self.logger.info(f"Successfully deactivated Go {version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deactivate Go {version}: {e}")
            return False

    def uninstall_version(self, version: str, install_path: Path) -> bool:
        """Uninstall Go version - 需要管理员权限"""
        try:
            # 检查是否有管理员权限
            if not self._check_admin_privileges():
                self.logger.error("Administrator privileges required to uninstall Go version")
                return False

            # 先停用版本
            self.deactivate_version(version, install_path)

            # 删除安装目录由manager处理
            self.logger.info(f"Go {version} marked for uninstallation")
            return True

        except Exception as e:
            self.logger.error(f"Failed to uninstall Go {version}: {e}")
            return False

    def get_installed_versions(self, base_path: Path) -> List[InstallationInfo]:
        """Get installed Go versions"""
        installed = []
        
        if not base_path.exists():
            return installed

        for version_dir in base_path.iterdir():
            if version_dir.is_dir():
                if self.verify_installation(version_dir.name, version_dir):
                    installed.append(InstallationInfo(
                        language="go",
                        version=version_dir.name,
                        install_path=version_dir
                    ))
        
        return installed

    def verify_installation(self, version: str, install_path: Path) -> bool:
        """Verify Go installation"""
        # Check for go executable
        go_exe = "go.exe" if platform.system() == "Windows" else "go"
        go_path = install_path / "bin" / go_exe
        
        return go_path.exists() and go_path.is_file()

    def get_version_info(self, version: str) -> Optional[LanguageVersion]:
        """Get information about a specific Go version"""
        # Fetch all versions and find the matching one
        all_versions = self.list_available_versions()
        for v in all_versions:
            if v.version == version:
                return v
        return None

    def get_executable_path(self, version: str, install_path: Path) -> Optional[Path]:
        """Get path to Go executable"""
        go_exe = "go.exe" if platform.system() == "Windows" else "go"
        go_path = install_path / "bin" / go_exe
        
        if go_path.exists():
            return go_path
        return None

    def get_environment_variables(self, version: str, install_path: Path) -> Dict[str, str]:
        """Get environment variables for Go"""
        return {
            "GOROOT": str(install_path)
        }

    def _check_admin_privileges(self) -> bool:
        """检查是否有管理员权限"""
        from ..utils.admin import is_admin
        return is_admin()

    def _set_go_env_vars(self, install_path: Path) -> bool:
        """设置Go环境变量"""
        from ..utils.admin import set_system_env_var

        # 设置GOROOT
        if not set_system_env_var("GOROOT", str(install_path)):
            return False

        # 设置GOPATH (用户工作空间，可选)
        gopath = Path.home() / "go"
        if not gopath.exists():
            gopath.mkdir(parents=True, exist_ok=True)
        set_system_env_var("GOPATH", str(gopath))

        return True

    def _remove_go_env_vars(self) -> bool:
        """移除Go环境变量"""
        from ..utils.admin import remove_system_env_var

        success = True
        if not remove_system_env_var("GOROOT"):
            success = False
        # 注意：GOPATH通常不删除，因为它是用户工作空间

        return success

    def _get_go_paths(self, install_path: Path) -> List[Path]:
        """获取Go需要添加到PATH的路径"""
        paths = []

        # Go bin目录
        bin_dir = install_path / "bin"
        if bin_dir.exists():
            paths.append(bin_dir)

        # GOPATH bin目录 (用户安装的Go工具)
        gopath = Path.home() / "go"
        gopath_bin = gopath / "bin"
        if gopath_bin.exists():
            paths.append(gopath_bin)

        return paths

    def _add_to_system_path(self, path: str) -> bool:
        """添加路径到系统PATH"""
        from ..utils.admin import add_to_system_path
        return add_to_system_path(path)

    def _remove_from_system_path(self, path: str) -> bool:
        """从系统PATH中移除路径"""
        from ..utils.admin import remove_from_system_path
        return remove_from_system_path(path)
