# LangVM Basic Usage Examples

This document provides examples of how to use LangVM to manage different programming language versions.

## Installation

First, install LangVM:

```bash
# Clone the repository
git clone https://github.com/yourusername/langvm.git
cd langvm

# Run the setup script
# On Linux/macOS:
bash scripts/setup.sh

# On Windows:
powershell -ExecutionPolicy Bypass -File scripts/setup.ps1
```

## Basic Commands

### List Supported Languages

```bash
langvm languages
```

Output:
```
Supported Languages:
  • go
  • java
  • nodejs
  • python
```

### List Available Versions

```bash
# List available Java versions
langvm list java

# List available Python versions
langvm list python

# List available Go versions
langvm list go

# List available Node.js versions
langvm list nodejs
```

### Install a Language Version

```bash
# Install Java 17
langvm install java 17

# Install Python 3.11.0
langvm install python 3.11.0

# Install Go 1.20.0
langvm install go 1.20.0

# Install Node.js 18.0.0
langvm install nodejs 18.0.0
```

### Switch Between Versions

```bash
# Use Java 17
langvm use java 17

# Use Python 3.11.0
langvm use python 3.11.0

# Use Go 1.20.0
langvm use go 1.20.0

# Use Node.js 18.0.0
langvm use nodejs 18.0.0
```

### Check Current Active Versions

```bash
# Check all active versions
langvm current

# Check specific language
langvm current java
langvm current python
```

### List Installed Versions

```bash
# List all installed versions
langvm installed

# List installed versions for specific language
langvm installed java
langvm installed python
```

### Uninstall a Version

```bash
# Uninstall Java 17
langvm uninstall java 17

# Uninstall Python 3.11.0
langvm uninstall python 3.11.0
```

## Environment Setup

After switching to a version, you need to update your shell environment:

### Bash/Zsh

```bash
# Add to your ~/.bashrc or ~/.zshrc
eval "$(langvm env)"

# Or manually for current session
eval "$(langvm env)"
```

### PowerShell

```powershell
# Add to your PowerShell profile
Invoke-Expression (langvm env --shell powershell)

# Or manually for current session
Invoke-Expression (langvm env --shell powershell)
```

## Workflow Examples

### Java Development

```bash
# Install multiple Java versions
langvm install java 8
langvm install java 11
langvm install java 17

# Switch to Java 11 for a legacy project
langvm use java 11
eval "$(langvm env)"
java -version  # Should show Java 11

# Switch to Java 17 for a modern project
langvm use java 17
eval "$(langvm env)"
java -version  # Should show Java 17
```

### Python Development

```bash
# Install multiple Python versions
langvm install python 3.9.0
langvm install python 3.10.0
langvm install python 3.11.0

# Use Python 3.11 for a new project
langvm use python 3.11.0
eval "$(langvm env)"
python --version  # Should show Python 3.11.0

# Create a virtual environment
python -m venv myproject
source myproject/bin/activate  # On Windows: myproject\Scripts\activate
```

### Go Development

```bash
# Install Go
langvm install go 1.20.0
langvm use go 1.20.0
eval "$(langvm env)"

# Verify installation
go version  # Should show go1.20.0

# Create a simple Go program
mkdir hello-go
cd hello-go
go mod init hello
echo 'package main

import "fmt"

func main() {
    fmt.Println("Hello, World!")
}' > main.go

go run main.go
```

### Node.js Development

```bash
# Install Node.js
langvm install nodejs 18.0.0
langvm use nodejs 18.0.0
eval "$(langvm env)"

# Verify installation
node --version  # Should show v18.0.0
npm --version

# Create a simple Node.js project
mkdir hello-node
cd hello-node
npm init -y
echo 'console.log("Hello, World!");' > index.js
node index.js
```

## Configuration

### View Configuration

```bash
langvm config
```

### Custom Configuration

Edit the configuration file at `~/.langvm/config.yaml`:

```yaml
# Custom installation directory
install_dir: "/opt/langvm/versions"

# Custom mirrors for faster downloads
mirrors:
  java: "https://your-mirror.com/java/"
  python: "https://your-mirror.com/python/"

# Proxy settings
proxy:
  enabled: true
  http: "http://proxy.company.com:8080"
  https: "https://proxy.company.com:8080"
```

## Troubleshooting

### Version Not Found

If a version is not found:

```bash
# Check available versions
langvm list java

# Make sure you're using the correct version format
langvm install java 17  # Not java-17 or jdk-17
```

### Environment Not Updated

If the environment is not updated after switching versions:

```bash
# Make sure to run the env command
eval "$(langvm env)"

# Or restart your shell
```

### Permission Issues

If you encounter permission issues:

```bash
# Make sure you have write permissions to the install directory
# Check the install directory
langvm config

# Or use a custom install directory in your config
```

### Download Failures

If downloads fail:

```bash
# Check your internet connection
# Check if you need proxy settings in config
# Try again with verbose output
langvm -v install java 17
```

## Advanced Usage

### Scripting

You can use LangVM in scripts:

```bash
#!/bin/bash
# Switch to specific versions for a project

langvm use java 17
langvm use nodejs 18.0.0
eval "$(langvm env)"

# Now run your build commands
./gradlew build
npm run build
```

### CI/CD Integration

```yaml
# GitHub Actions example
- name: Setup Java
  run: |
    langvm install java 17
    langvm use java 17
    eval "$(langvm env)"
    
- name: Setup Node.js
  run: |
    langvm install nodejs 18.0.0
    langvm use nodejs 18.0.0
    eval "$(langvm env)"
```

This covers the basic usage of LangVM. For more advanced features and configuration options, see the full documentation.
