#!/usr/bin/env python3
"""
LangVM 演示脚本
展示 LangVM 的主要功能
"""

import subprocess
import sys
import time
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

console = Console()

def run_command(cmd, description):
    """运行命令并显示结果"""
    console.print(f"\n[bold blue]>>> {description}[/bold blue]")
    console.print(f"[dim]$ {cmd}[/dim]")
    
    try:
        result = subprocess.run(
            cmd.split(),
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            console.print(result.stdout)
        else:
            console.print(f"[red]Error: {result.stderr}[/red]")
            
    except subprocess.TimeoutExpired:
        console.print("[yellow]Command timed out[/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
    
    time.sleep(1)

def main():
    """主演示函数"""
    
    # 显示欢迎信息
    welcome_text = Text()
    welcome_text.append("LangVM - 通用编程语言版本管理器\n", style="bold green")
    welcome_text.append("Universal Language Version Manager\n\n", style="bold blue")
    welcome_text.append("本演示将展示 LangVM 的主要功能", style="dim")
    
    console.print(Panel(welcome_text, title="欢迎使用 LangVM", border_style="green"))
    
    input("\n按 Enter 键开始演示...")
    
    # 1. 显示帮助信息
    run_command("langvm --help", "显示帮助信息")
    
    # 2. 检查管理员权限
    run_command("langvm admin", "检查管理员权限状态")
    
    # 3. 显示支持的语言
    run_command("langvm languages", "显示支持的编程语言")
    
    # 4. 列出 Java 可用版本
    run_command("langvm list java", "列出 Java 可用版本")
    
    # 5. 显示配置信息
    run_command("langvm config", "显示配置信息")
    
    # 6. 查看当前激活的版本
    run_command("langvm current", "查看当前激活的版本")
    
    # 7. 查看已安装的版本
    run_command("langvm installed", "查看已安装的版本")

    # 8. 演示系统级切换帮助
    run_command("langvm use --help", "查看系统级切换选项")

    # 演示完成
    console.print("\n" + "="*60)
    console.print("[bold green]演示完成！[/bold green]")
    console.print("\n[bold yellow]接下来你可以尝试：[/bold yellow]")

    console.print("\n[bold blue]基本操作：[/bold blue]")
    console.print("• [cyan]langvm install java 17[/cyan] - 安装 Java 17")
    console.print("• [cyan]langvm install python 3.11.0[/cyan] - 安装 Python 3.11")
    console.print("• [cyan]langvm install go 1.20.0[/cyan] - 安装 Go 1.20")
    console.print("• [cyan]langvm install nodejs 18.0.0[/cyan] - 安装 Node.js 18")

    console.print("\n[bold blue]用户级切换：[/bold blue]")
    console.print("• [cyan]langvm use java 17[/cyan] - 切换到 Java 17")
    console.print("• [cyan]eval \"$(langvm env)\"[/cyan] - 激活环境")

    console.print("\n[bold blue]系统级切换 (需要管理员权限)：[/bold blue]")
    console.print("• [cyan]langvm use --system java 17[/cyan] - 系统级切换 Java")
    console.print("• [cyan]langvm use --system python 3.11.0[/cyan] - 系统级切换 Python")
    console.print("• [cyan]langvm use --system go 1.20.0[/cyan] - 系统级切换 Go")
    console.print("• [cyan]langvm use --system nodejs 18.0.0[/cyan] - 系统级切换 Node.js")

    console.print("\n[bold yellow]环境变量说明：[/bold yellow]")
    console.print("系统级切换会自动设置以下环境变量：")
    console.print("• Java: JAVA_HOME")
    console.print("• Python: PYTHON_HOME, PYTHONPATH")
    console.print("• Go: GOROOT, GOPATH")
    console.print("• Node.js: NODE_PATH")

    console.print("\n[dim]更多信息请查看 README_CN.md[/dim]")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        console.print("\n[yellow]演示被用户中断[/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"\n[red]演示出错: {e}[/red]")
        sys.exit(1)
